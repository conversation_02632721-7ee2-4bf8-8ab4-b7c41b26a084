# Create Smart TODOs

I'll analyze recent operations and create contextual TODO comments in your code.

First, let me understand your project standards:
- **Read** README.md for project conventions
- **Read** CONTRIBUTING.md for code style guidelines
- **Grep** existing TODOs to match your format
- **Read** documentation for technical context

I'll examine what just happened in our session:
- Security scan findings that need fixes
- Test failures requiring attention
- Code review issues to address
- Architecture improvements identified
- Performance optimizations suggested

Using native tools to analyze context:
- **Grep tool** to find related code sections
- **Read tool** to understand implementation details
- **TodoWrite** to track what TODOs I'm creating

For each TODO I'll:
1. Find the exact location where it belongs
2. Analyze surrounding code for context
3. Create clear, actionable TODO comments
4. Include priority and category markers

**TODO Format Adaptation:**
I'll match your project's existing TODO style:
- Check if you use categories like `[Security]` or `(SECURITY)`
- Match comment style: `//`, `#`, `/* */`, etc.
- Follow any ticket reference patterns your project uses
- Respect line length limits from your linter

**Smart Context References:**
I'll create TODOs that reference the source of the issue:
- Security findings will reference the scan line number
- Performance issues will include measured thresholds
- Bug fixes will reference failing test locations
- Refactoring needs will reference architectural patterns

I'll use intelligent placement:
- Security TODOs near vulnerable code
- Performance TODOs at bottleneck locations
- Bug fixes where errors occur
- Architecture TODOs at integration points

After creating TODOs, I'll ask: "How would you like to track these?"
- Convert to GitHub issues with `/todos-to-issues`
- Keep as code comments for gradual resolution
- Generate summary report for team review

**Important**: I will NEVER:
- Add "Generated by Claude" or AI attribution in TODOs
- Create vague or non-actionable TODOs
- Place TODOs in random locations
- Overwhelm code with excessive comments

This creates a clear technical debt roadmap directly in your codebase.