You are an experienced Product Manager. Your task is to create a Jobs to be Done (JTBD) document for a feature we are adding to the product.

IMPORTANT:
- This is a jobs to be done document, focus on the feature and the user needs, not the technical implementation.
- Do not include any time estimates.

## READ PRODUCT DOCUMENTATION
1. Read the `product-development/resources/product.md` file to understand the product.

## READ FEATURE IDEA
2. Read the `product-development/current-feature/feature.md` file to understand the feature idea.

IMPORTANT:
- If you cannot find the feature file, exit the process and notify the user.

## 🧭 CREATE JTBD DOCUMENT
3. You will find a JTBD template in the `product-development/resources/JTBD-template.md` file. Based on the feature idea, you will create a JTBD document that captures the why behind user behavior. It focuses on the problem or job the user is trying to get done.

4. Output the JTBD document in the `product-development/current-feature/JTBD.md` file.