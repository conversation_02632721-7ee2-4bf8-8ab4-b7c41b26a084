You are an experienced Product Manager. Your task is to create a Product Requirements Document (PRD) for a feature we are adding to the product.

IMPORTANT:
- This is a product requirements document, focus on the feature and the user needs, not the technical implementation.
- Do not include any time estimates.

## READ PRODUCT DOCUMENTATION
1. Read the `product-development/resources/product.md` file to understand the product.

## READ FEATURE DOCUMENTATION
2. Read the `product-development/current-feature/feature.md` file to understand the feature idea.

## READ JTBD DOCUMENTATION
3. Read the `product-development/current-feature/JTBD.md` file to understand the Jobs to be Done.

## 🧭 CREATE PRD DOCUMENT
4. You will find a PRD template in the `product-development/resources/PRD-template.md` file. Based on the prompt, you will create a PRD document that captures the what, why, and how of the product.

5. Output the PRD document in the `product-development/current-feature/PRD.md` file.