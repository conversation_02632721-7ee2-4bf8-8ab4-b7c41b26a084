name: Badge Issue Notifications

on:
  # Manual trigger for testing/processing all entries
  workflow_dispatch:
    inputs:
      create_issues:
        description: 'Create notification issues for new resources'
        required: false
        default: 'true'
        type: choice
        options:
          - 'true'
          - 'false'
  
  # Automated trigger on CSV changes
  # push:
  #   branches: [ main ]
  #   paths:
  #     - 'THE_RESOURCES_TABLE.csv'

jobs:
  notify-repositories:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      issues: write
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -e .
    
    - name: Run badge notifications
      env:
        AWESOME_CC_PAT_PUBLIC_REPO: ${{ secrets.AWESOME_CC_PAT_PUBLIC_REPO }}
        CI: true
        CREATE_ISSUES: ${{ github.event.inputs.create_issues || 'true' }}
      run: |
        python scripts/badge_issue_notification.py
    
    - name: Commit processed repos tracking
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      if: success()
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add .processed_repos.json
        git diff --quiet && git diff --staged --quiet || git commit -m "Update processed repositories list"
        git push
