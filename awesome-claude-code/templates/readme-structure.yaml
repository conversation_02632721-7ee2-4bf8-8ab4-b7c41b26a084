# README Structure Configuration
# This file defines the structure and ordering of sections in the generated README

sections:
  - id: workflows
    title: "Workflows & Knowledge Guides"
    description: |
      > A **workflow** is a tightly coupled set of Claude Code-native resources that facilitate specific projects
    source: csv
    category: "Workflows & Knowledge Guides"
    icon: "🧠"

  - id: tooling
    title: "Tooling"
    description: |
      > **Tooling** denotes applications that are built on top of Claude Code and consist of more components than slash-commands and `CLAUDE.md` files
    source: csv
    category: "Tooling"
    icon: "🧰"
    subsections:
      - id: ide-integrations
        title: "IDE Integrations"
        sub_category: "IDE Integrations"

  - id: hooks
    title: "Hooks"
    description: |
      > **Hooks** are a brand new API for Claude Code that allows users to activate commands and run scripts at different points in <PERSON>'s agentic lifecycle.

      **[Experimental]** - The resources listed in this section have not been fully vetted and may not work as expected, given the bleeding-edge nature of Claude Code hooks. Nevertheless, I wished to include them at least as a source of inspiration and to explore this unknown terrain. YMMV!
    source: csv
    category: "Hooks"
    icon: "🪝"

  - id: slash-commands
    title: "Slash-Commands"
    description: ""
    source: csv
    category: "Slash-Commands"
    icon: "🔪"
    subsections:
      - id: version-control-git
        title: "Version Control & Git"
        sub_category: "Version Control & Git"
      - id: code-analysis-testing
        title: "Code Analysis & Testing"
        sub_category: "Code Analysis & Testing"
      - id: context-loading-priming
        title: "Context Loading & Priming"
        sub_category: "Context Loading & Priming"
      - id: documentation-changelogs
        title: "Documentation & Changelogs"
        sub_category: "Documentation & Changelogs"
      - id: ci-deployment
        title: "CI / Deployment"
        sub_category: "CI / Deployment"
      - id: project-task-management
        title: "Project & Task Management"
        sub_category: "Project & Task Management"
      - id: miscellaneous
        title: "Miscellaneous"
        sub_category: "Miscellaneous"

  - id: claude-md-files
    title: "CLAUDE.md Files"
    description: |
      > **`CLAUDE.md` files** are files that contain important guidelines and context-specfic information or instructions that help Claude Code to better understand your project and your coding standards
    source: csv
    category: "CLAUDE.md Files"
    icon: "📂"
    subsections:
      - id: language-specific
        title: "Language-Specific"
        sub_category: "Language-Specific"
      - id: domain-specific
        title: "Domain-Specific"
        sub_category: "Domain-Specific"
      - id: project-scaffolding-mcp
        title: "Project Scaffolding & MCP"
        sub_category: "Project Scaffolding & MCP"

  - id: official-documentation
    title: "Official Documentation"
    description: |
      > Links to some of Anthropic's terrific documentation and resources regarding Claude Code

      <!--lint disable double-link-->
    source: csv
    category: "Official Documentation"
    icon: "🏛️"

# Table of Contents formatting
toc:
  style: custom # custom style to match existing format
  symbol: "▪"
  subsymbol: "▫"
  indent: "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
  subindent: "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
