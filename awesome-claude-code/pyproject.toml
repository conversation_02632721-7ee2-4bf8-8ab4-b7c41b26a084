[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "awesome-claude-code"
version = "1.0.0"
description = "A curated list of slash-commands, CLAUDE.md files, CLI tools, and other resources for enhancing Claude Code workflows"
authors = [{ name = "Really Him", email = "<EMAIL>" }]
readme = "README.md"
license = { file = "LICENSE" }
requires-python = ">=3.11"
dependencies = ["PyGithub>=2.1.1"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

[project.optional-dependencies]
dev = [
    "requests>=2.31.0",
    "python-dotenv>=1.0.0",
    "types-requests>=2.31.0",
    "ruff>=0.1.0",
    "pre-commit>=3.5.0",
]

[project.urls]
"Homepage" = "https://github.com/anthropics/awesome-claude-code"
"Bug Tracker" = "https://github.com/anthropics/awesome-claude-code/issues"
"Repository" = "https://github.com/anthropics/awesome-claude-code"

[tool.ruff]
target-version = "py311"
line-length = 120

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "N",   # pep8-naming
    "UP",  # pyupgrade
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "SIM", # flake8-simplify
]
ignore = [
    "E501", # line too long (handled by formatter)
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"] # unused imports in __init__ files

[tool.ruff.lint.isort]
known-first-party = ["scripts"]

[tool.setuptools.packages.find]
where = ["."]
include = ["scripts*"]

[tool.setuptools.package-data]
"scripts" = ["*.csv"]
