["CommE2E/comm", "Consiliency/Flutter-Structurizr", "Family-IT-Guy/perplexity-mcp", "Fimeg/NetworkChronicles", "GaloyMoney/lana-bank", "GowayLee/cchooks", "Graphlet-AI/eridu", "<PERSON><PERSON><PERSON>/claude-simone", "KarpelesLab/tpl", "LamoomAI/lamoom-python", "Layr-Labs/avs-vibe-developer-guide", "Maciek-roboblog/<PERSON>-Code-Usage-Monitor", "Mjvolk3/torchcell", "ParetoSecurity/pareto-mac", "TuckerTucker/tkr-portfolio", "Veraticus/nix-config", "Wirasm/claudecode-utils", "alexei-led/aws-mcp-server", "anthropics/anthropic-quickstarts", "anthropics/claude-code-action", "arkavo-org/opentdf-rs", "badass-courses/course-builder", "basicmachines-co/basic-memory", "berrydev-ai/blockdoc-python", "beyondcode/claude-hooks-sdk", "chrisleyva/todo-slash-command", "claude-did-this/claude-hub", "cloudartisan/cloudartisan.github.io", "dagger/container-use", "danielscholl/mvn-mcp-server", "ddisisto/si", "didalgolab/ai-intellij-plugin", "different-ai/note-companion", "disler/just-prompt", "eastlondoner/cursor-tools", "elizaOS/elizaos.github.io", "ethpandaops/xatu-data", "evmts/tevm-monorepo", "expectedparrot/edsl", "eyal<PERSON><PERSON><PERSON>/claude-task-master", "giselles-ai/giselle", "grahama1970/claude-code-mcp-enhanced", "grahama1970/claude-task-runner", "greggh/claude-code.nvim", "hackdays-io/toban-contribution-viewer", "harperreed/dotfiles", "hashintel/hash", "inkline/inkline", "jere<PERSON><PERSON>en/kotlinter-gradle", "<PERSON><PERSON><PERSON>/Narraitor", "john<PERSON><PERSON>/claude-hooks", "kelp/webdown", "kingler/n8n_agent", "langchain-ai/langgraphjs", "liam-hq/liam", "mattgo<PERSON><PERSON>/jsbeeb", "metabase/metabase", "nizos/tdd-guard", "nyatinte/ccexp", "okuvshynov/cubestat", "parruda/claude-swarm", "possibilities/claude-composer", "ruvnet/claude-code-flow", "rygwdn/slack-tools", "ryoppippi/ccusage", "rzykov/metabase", "scopecraft/command", "sgcarstrends/backend", "slunsford/coffee-analytics", "smtg-ai/claude-squad", "<PERSON><PERSON><PERSON><PERSON>/Guitar", "sota<PERSON><PERSON><PERSON>/dotfiles", "spylang/spy", "steadycursor/steadystart", "stevemolitor/claude-code.el", "stravu/crystal", "taddyorg/inkverse", "to4iki/ai-project-rules", "touchlab/DroidconKotlin", "<PERSON><PERSON><PERSON><PERSON>/giselle", "wcygan/dotfiles", "yzy<PERSON>ev/AI-Engineering-Structure", "zscott/pane", "zuplo/docs"]