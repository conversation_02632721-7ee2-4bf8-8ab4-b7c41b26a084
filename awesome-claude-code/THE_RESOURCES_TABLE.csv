ID,Display Name,Category,Sub-Category,Primary Link,Secondary Link,Author Name,Author Link,Active,Date Added,Last Modified,Last Checked,License,Description
wf-8376d518,Blogging Platform Instructions,Workflows & Knowledge Guides,,https://github.com/cloudartisan/cloudartisan.github.io/tree/main/.claude/commands,,cloudartisan,https://github.com/cloudartisan,TRUE,2025-07-29,,2025-07-29:18-37-05,CC-BY-SA-4.0,"Provides a well-structured set of commands for publishing and maintaining a blogging platform, including commands for creating posts, managing categories, and handling media files."
wf-935cc6ae,ClaudeLog,Workflows & Knowledge Guides,,https://claudelog.com,,InventorBlack,https://www.reddit.com/user/inventor_black/,TRUE,,,2025-07-29:18-37-05,NOT_FOUND,"A comprehensive knowledge base with detailed breakdowns of advanced [mechanics](https://claudelog.com/mechanics/you-are-the-main-thread/) including [CLAUDE.md best practices](https://claudelog.com/mechanics/claude-md-supremacy), practical technique guides like [plan mode](https://claudelog.com/mechanics/plan-mode), [ultrathink](https://claudelog.com/faqs/what-is-ultrathink/), [sub-agents](https://claudelog.com/mechanics/task-agent-tools/), [agent-first design](https://claudelog.com/mechanics/agent-first-design/) and [configuration guides](https://claudelog.com/configuration)."
wf-b98b3b2d,Context Priming,Workflows & Knowledge Guides,,https://github.com/disler/just-prompt/tree/main/.claude/commands,,disler,https://github.com/disler,TRUE,,,2025-07-29:18-37-05,NOT_FOUND,Provides a systematic approach to priming Claude Code with comprehensive project context through specialized commands for different project scenarios and development contexts.
wf-43a18fc2,n8n_agent,Workflows & Knowledge Guides,,https://github.com/kingler/n8n_agent/tree/main/.claude/commands,,kingler,https://github.com/kingler,TRUE,,,2025-07-29:18-37-06,NOT_FOUND,"Amazing comprehensive set of comments for code analysis, QA, design, documentation, project structure, project management, optimization, and many more."
wf-1fddaad0,Project Bootstrapping and Task Management,Workflows & Knowledge Guides,,https://github.com/steadycursor/steadystart/tree/main/.claude/commands,,steadycursor,https://github.com/steadycursor,TRUE,,,2025-07-29:18-37-06,NOT_FOUND,"Provides a structured set of commands for bootstrapping and managing a new project, including meta-commands for creating and editing custom slash-commands."
wf-bdb46cd1,"Project Management, Implementation, Planning, and Release",Workflows & Knowledge Guides,,https://github.com/scopecraft/command/tree/main/.claude/commands,,scopecraft,https://github.com/scopecraft,TRUE,,,2025-07-29:18-37-06,NOT_FOUND,Really comprehensive set of commands for all aspects of SDLC.
wf-42a8d5a5,Project Workflow System,Workflows & Knowledge Guides,,https://github.com/harperreed/dotfiles/tree/master/.claude/commands,,harperreed,https://github.com/harperreed,TRUE,2025-07-29,,2025-07-29:18-37-07,NOT_FOUND,"A set of commands that provide a comprehensive workflow system for managing projects, including task management, code review, and deployment processes."
wf-eee9a073,Shipping Real Code w/ Claude,Workflows & Knowledge Guides,,https://diwank.space/field-notes-from-shipping-real-code-with-claude,,Diwank,https://github.com/creatorrr,TRUE,,,2025-07-29:18-37-07,NOT_FOUND,"A detailed blog post explaining the author's process for shipping a product with Claude Code, including CLAUDE.md files and other interesting resources."
wf-b4fe16fa,Simone,Workflows & Knowledge Guides,,https://github.com/Helmi/claude-simone,,Helmi,https://github.com/Helmi,TRUE,2025-07-29,2025-07-10:18-53-27,2025-07-29:18-37-08,MIT,"A broader project management workflow for Claude Code that encompasses not just a set of commands, but a system of documents, guidelines, and processes to facilitate project planning and execution."
wf-b6f047e2,Slash-commands megalist,Workflows & Knowledge Guides,,https://github.com/wcygan/dotfiles/tree/d8ab6b9f5a7a81007b7f5fa3025d4f83ce12cc02/claude/commands,,wcygan,https://github.com/wcygan,TRUE,2025-07-29,,2025-07-29:18-37-08,NOT_FOUND,"A pretty stunning list (88 at the time of this post!) of slash-commands ranging from agent orchestration, code review, project management, security, documentation, self-assessment, almost anything you can dream of."
tool-984936a7,Claude Code Chat,Tooling,IDE Integrations,https://marketplace.visualstudio.com/items?itemName=AndrePimenta.claude-code-chat,,andrepimenta,https://github.com/andrepimenta,TRUE,,,2025-07-18:02-03-39,&copy;,An elegant and user-friendly Claude Code chat interface for VS Code.
tool-941ef941,claude-code.el,Tooling,IDE Integrations,https://github.com/stevemolitor/claude-code.el,,stevemolitor,https://github.com/stevemolitor,TRUE,2025-07-29,2025-07-08:13-59-24,2025-07-29:18-37-08,Apache-2.0,An Emacs interface for Claude Code CLI.
tool-0607ef06,claude-code.nvim,Tooling,IDE Integrations,https://github.com/greggh/claude-code.nvim,,greggh,https://github.com/greggh,TRUE,2025-07-29,2025-07-02:19-40-30,2025-07-29:18-37-09,MIT,A seamless integration between Claude Code AI assistant and Neovim.
tool-1c31f36c,crystal,Tooling,IDE Integrations,https://github.com/stravu/crystal,,stravu,https://github.com/stravu,TRUE,2025-07-29,2025-07-11:18-53-26,2025-07-29:18-37-10,MIT,"A full-fledged desktop application for orchestrating, monitoring, and interacting with Claude Code agents."
tool-631dbe0f,CC Usage,Tooling,,https://github.com/ryoppippi/ccusage,,ryoppippi,https://github.com/ryoppippi,TRUE,2025-07-29,2025-07-11:17-41-42,2025-07-29:18-37-11,MIT,"Handy CLI tool for managing and analyzing Claude Code usage, based on analyzing local Claude Code logs. Presents a nice dashboard regarding cost information, token consumption, etc."
tool-b7bb841e,ccexp,Tooling,,https://github.com/nyatinte/ccexp,https://www.npmjs.com/package/ccexp,nyatinte,https://github.com/nyatinte,TRUE,2025-07-29,,2025-07-29:18-37-11,MIT,Interactive CLI tool for discovering and managing Claude Code configuration files and slash commands with a beautiful terminal UI.
tool-3b3bedca,Claude Code Flow,Tooling,,https://github.com/ruvnet/claude-code-flow,,ruvnet,https://github.com/ruvnet,TRUE,2025-07-29,2025-07-10:05-56-05,2025-07-29:18-37-12,MIT,"This mode serves as a code-first orchestration layer, enabling Claude to write, edit, test, and optimize code autonomously across recursive agent cycles."
tool-ca599740,Claude Code Usage Monitor,Tooling,,https://github.com/Maciek-roboblog/Claude-Code-Usage-Monitor,,Maciek-roboblog,https://github.com/Maciek-roboblog,TRUE,2025-07-29,,2025-07-29:18-37-13,MIT,"A real-time terminal-based tool for monitoring Claude Code token usage. It shows live token consumption, burn rate, and predictions for token depletion. Features include visual progress bars, session-aware analytics, and support for multiple subscription plans."
tool-552cdcdf,Claude Composer,Tooling,,https://github.com/possibilities/claude-composer,,Mike Bannister,https://github.com/possibilities,TRUE,2025-07-29,2025-07-10:00-27-19,2025-07-29:18-37-14,Unlicense,A tool that adds small enhancements to Claude Code.
tool-ca25af98,Claude Hub,Tooling,,https://github.com/claude-did-this/claude-hub,,Claude Did This,https://github.com/claude-did-this,TRUE,2025-07-29,2025-06-20:16-16-08,2025-07-29:18-37-14,NOT_FOUND,"A webhook service that connects Claude Code to GitHub repositories, enabling AI-powered code assistance directly through pull requests and issues. This integration allows Claude to analyze repositories, answer technical questions, and help developers understand and improve their codebase through simple @mentions."
tool-5d0685f2,Claude Squad,Tooling,,https://github.com/smtg-ai/claude-squad,,smtg-ai,https://github.com/smtg-ai,TRUE,2025-07-29,2025-07-08:04-29-10,2025-07-29:18-37-15,AGPL-3.0,"Claude Squad is a terminal app that manages multiple Claude Code, Codex (and other local agents including Aider) in separate workspaces, allowing you to work on multiple tasks simultaneously."
tool-1af2fe4c,Claude Swarm,Tooling,,https://github.com/parruda/claude-swarm,,parruda,https://github.com/parruda,TRUE,2025-07-29,2025-07-11:03-04-27,2025-07-29:18-37-16,MIT,Launch Claude Code session that is connected to a swarm of Claude Code Agents.
tool-a1e3d643,Claude Task Master,Tooling,,https://github.com/eyaltoledano/claude-task-master,,eyaltoledano,https://github.com/eyaltoledano,TRUE,2025-07-29,2025-07-05:05-18-20,2025-07-29:18-37-16,NOASSERTION,"A task management system for AI-driven development with Claude, designed to work seamlessly with Cursor AI."
tool-f81477b3,Claude Task Runner,Tooling,,https://github.com/grahama1970/claude-task-runner,,grahama1970,https://github.com/grahama1970,TRUE,2025-07-29,2025-05-13:23-08-49,2025-07-29:18-37-17,NOT_FOUND,"A specialized tool to manage context isolation and focused task execution with Claude Code, solving the critical challenge of context length limitations and task focus when working with Claude on complex, multi-step projects."
tool-af235370,Container Use,Tooling,,https://github.com/dagger/container-use,,dagger,https://github.com/dagger,TRUE,2025-07-29,2025-07-10:21-53-40,2025-07-29:18-37-18,Apache-2.0,Development environments for coding agents. Enable multiple agents to work safely and independently with your preferred stack.
tool-8d2e7868,tweakcc,Tooling,,https://github.com/Piebald-AI/tweakcc,,Piebald-AI,https://github.com/Piebald-AI,TRUE,,,2025-07-29:18-37-19,MIT,Command-line tool to customize your Claude Code styling.
hook-37bef012,CC Notify,Hooks,,https://github.com/dazuiba/CCNotify,,dazuiba,https://github.com/dazuiba,TRUE,,2025-07-29:18-50-27,MIT,"CCNotify provides desktop notifications for Claude Code, alerting you to input needs or task completion, with one-click jumps back to VS Code and task duration display.",
hook-26657310,cchooks,Hooks,,https://github.com/GowayLee/cchooks,https://pypi.org/project/cchooks/,GowayLee,https://github.com/GowayLee,TRUE,2025-07-29,,2025-07-29:18-37-19,MIT,"A lightweight Python SDK with a clean API and good documentation; simplifies the process of writing hooks and integrating them into your codebase, providing a nice abstraction over the JSON configuration files."
hook-61fc561a,claude-code-hooks-sdk,Hooks,,https://github.com/beyondcode/claude-hooks-sdk,,beyondcode,https://github.com/beyondcode,TRUE,2025-07-29,2025-07-03:20-18-10,2025-07-29:18-37-20,MIT,"A Laravel-inspired PHP SDK for building Claude Code hook responses with a clean, fluent API. This SDK makes it easy to create structured JSON responses for Claude Code hooks using an expressive, chainable interface."
hook-ff4a072b,claude-hooks,Hooks,,https://github.com/johnlindquist/claude-hooks,,John Lindquist,https://github.com/johnlindquist,TRUE,2025-07-29,2025-07-11:21-03-23,2025-07-29:18-37-21,MIT,A TypeScript-based system for configuring and customizing Claude Code hooks with a powerful and flexible interface.
hook-edd83641,"Linting, testing, and notifications (in go)",Hooks,,https://github.com/Veraticus/nix-config/tree/main/home-manager/claude-code/hooks,,Josh Symonds,https://github.com/Veraticus,TRUE,2025-07-29,,2025-07-29:18-37-21,MIT,"Nice set of hooks for enforcing code quality (linting, testing, notifications), with a nice configuration setup as well."
hook-2b995e52,TDD Guard,Hooks,,https://github.com/nizos/tdd-guard,,Nizar Selander,https://github.com/nizos,TRUE,2025-07-29,2025-07-13:19-50-03,2025-07-29:18-37-22,MIT,A hooks-driven system that monitors file operations in real-time and blocks changes that violate TDD principles.
cmd-19f297bd,/build-react-app,Slash-Commands,CI / Deployment,https://github.com/wmjones/wyatt-personal-aws/blob/main/.claude/commands/build-react-app.md,,wmjones,https://github.com/wmjones,FALSE,,,2025-07-29:18-37-22,NOT_FOUND,"Builds React applications locally with intelligent error handling, creating specific tasks for build failures and providing appropriate server commands based on build results."
cmd-39a87802,/release,Slash-Commands,CI / Deployment,https://github.com/kelp/webdown/blob/main/.claude/commands/release.md,,kelp,https://github.com/kelp,TRUE,2025-07-29,2025-03-22:05-33-16,2025-07-29:18-37-23,MIT,"Manages software releases by updating changelogs, reviewing README changes, evaluating version increments, and documenting release changes for better version tracking."
cmd-88d84cb6,/run-ci,Slash-Commands,CI / Deployment,https://github.com/hackdays-io/toban-contribution-viewer/blob/main/.claude/commands/run-ci.md,,hackdays-io,https://github.com/hackdays-io,TRUE,,2025-04-22:01-44-25,2025-07-29:18-37-23,NOT_FOUND,"Activates virtual environments, runs CI-compatible check scripts, iteratively fixes errors, and ensures all tests pass before completion."
cmd-fdc46b4a,/run-pre-commit,Slash-Commands,CI / Deployment,https://github.com/wmjones/wyatt-personal-aws/blob/main/.claude/commands/run-pre-commit.md,,wmjones,https://github.com/wmjones,FALSE,,,2025-07-29:18-37-24,NOT_FOUND,"Runs pre-commit checks with intelligent results handling, analyzing outputs, creating tasks for issue fixing, and integrating with task management systems."
cmd-884d2f7b,/analyze-code,Slash-Commands,Code Analysis & Testing,https://github.com/Hkgstax/VALUGATOR/blob/main/.claude/commands/analyze-code.md,,Hkgstax,https://github.com/Hkgstax,FALSE,,,2025-07-29:18-37-24,NOT_FOUND,"Reviews code structure and identifies key components, mapping relationships between elements and suggesting targeted improvements for better architecture and performance."
cmd-193fe5e1,/check,Slash-Commands,Code Analysis & Testing,https://github.com/rygwdn/slack-tools/blob/main/.claude/commands/check.md,,rygwdn,https://github.com/rygwdn,TRUE,2025-07-29,2025-05-06:17-13-58,2025-07-29:18-37-25,NOT_FOUND,"Performs comprehensive code quality and security checks, featuring static analysis integration, security vulnerability scanning, code style enforcement, and detailed reporting."
cmd-9944dc47,/clean,Slash-Commands,Code Analysis & Testing,https://github.com/Graphlet-AI/eridu/blob/main/.claude/commands/clean.md,,Graphlet-AI,https://github.com/Graphlet-AI,TRUE,2025-07-29,2025-05-16:04-28-34,2025-07-29:18-37-25,Apache-2.0,"Addresses code formatting and quality issues by fixing black formatting problems, organizing imports with isort, resolving flake8 linting issues, and correcting mypy type errors."
cmd-f77c03b5,/code_analysis,Slash-Commands,Code Analysis & Testing,https://github.com/kingler/n8n_agent/blob/main/.claude/commands/code_analysis.md,,kingler,https://github.com/kingler,TRUE,2025-07-29,2025-05-16:17-30-29,2025-07-29:18-37-26,NOT_FOUND,"Provides a menu of advanced code analysis commands for deep inspection, including knowledge graph generation, optimization suggestions, and quality evaluation."
cmd-e6804b12,/implement-issue,Slash-Commands,Code Analysis & Testing,https://github.com/cmxela/thinkube/blob/main/.claude/commands/implement-issue.md,,cmxela,https://github.com/cmxela,FALSE,,,2025-07-29:18-37-26,NOT_FOUND,"Implements GitHub issues following strict project guidelines, complete implementation checklists, variable naming conventions, testing procedures, and documentation requirements."
cmd-0ff45c34,/implement-task,Slash-Commands,Code Analysis & Testing,https://github.com/Hkgstax/VALUGATOR/blob/main/.claude/commands/implement-task.md,,Hkgstax,https://github.com/Hkgstax,FALSE,,,2025-07-29:18-37-26,NOT_FOUND,"Approaches task implementation methodically by thinking through strategy step-by-step, evaluating different approaches, considering tradeoffs, and implementing the best solution."
cmd-c76ed84c,/optimize,Slash-Commands,Code Analysis & Testing,https://github.com/to4iki/ai-project-rules/blob/main/.claude/commands/optimize.md,,to4iki,https://github.com/to4iki,TRUE,2025-07-29,2025-04-24:16-18-21,2025-07-29:18-37-27,MIT,"Analyzes code performance to identify bottlenecks, proposing concrete optimizations with implementation guidance for improved application performance."
cmd-3c922eaa,/repro-issue,Slash-Commands,Code Analysis & Testing,https://github.com/rzykov/metabase/blob/master/.claude/commands/repro-issue.md,,rzykov,https://github.com/rzykov,TRUE,2025-07-29,2025-04-08:08-37-04,2025-07-29:18-37-28,NOASSERTION,"Creates reproducible test cases for GitHub issues, ensuring tests fail reliably and documenting clear reproduction steps for developers."
cmd-1ba4d44c,/task-breakdown,Slash-Commands,Code Analysis & Testing,https://github.com/Hkgstax/VALUGATOR/blob/main/.claude/commands/task-breakdown.md,,Hkgstax,https://github.com/Hkgstax,FALSE,,,2025-07-29:18-37-28,NOT_FOUND,"Analyzes feature requirements, identifies components and dependencies, creates manageable tasks, and sets priorities for efficient feature implementation."
cmd-051321ab,/tdd,Slash-Commands,Code Analysis & Testing,https://github.com/zscott/pane/blob/main/.claude/commands/tdd.md,,zscott,https://github.com/zscott,TRUE,2025-07-29,2025-03-06:13-02-46,2025-07-29:18-37-29,NOT_FOUND,"Guides development using Test-Driven Development principles, enforcing Red-Green-Refactor discipline, integrating with git workflow, and managing PR creation."
cmd-cccacca2,/tdd-implement,Slash-Commands,Code Analysis & Testing,https://github.com/jerseycheese/Narraitor/blob/feature/issue-227-ai-suggestions/.claude/commands/tdd-implement.md,,jerseycheese,https://github.com/jerseycheese,FALSE,,,2025-07-29:18-37-29,MIT,"Implements Test-Driven Development by analyzing feature requirements, creating tests first (red), implementing minimal passing code (green), and refactoring while maintaining tests."
cmd-7991e9fb,/testing_plan_integration,Slash-Commands,Code Analysis & Testing,https://github.com/buster-so/buster/blob/main/api/.claude/commands/testing_plan_integration.md,,buster-so,https://github.com/buster-so,FALSE,,,2025-07-29:18-37-29,NOASSERTION,"Creates inline Rust-style tests, suggests refactoring for testability, analyzes code challenges, and creates comprehensive test coverage for robust code."
cmd-01b57069,/context-prime,Slash-Commands,Context Loading & Priming,https://github.com/elizaOS/elizaos.github.io/blob/main/.claude/commands/context-prime.md,,elizaOS,https://github.com/elizaOS,TRUE,2025-07-29,2025-04-02:21-36-33,2025-07-29:18-37-30,MIT,"Primes Claude with comprehensive project understanding by loading repository structure, setting development context, establishing project goals, and defining collaboration parameters."
cmd-82556482,/initref,Slash-Commands,Context Loading & Priming,https://github.com/okuvshynov/cubestat/blob/main/.claude/commands/initref.md,,okuvshynov,https://github.com/okuvshynov,TRUE,2025-07-29,2025-04-18:15-48-49,2025-07-29:18-37-30,MIT,"Initializes reference documentation structure with standard doc templates, API reference setup, documentation conventions, and placeholder content generation."
cmd-e7fde689,/load-llms-txt,Slash-Commands,Context Loading & Priming,https://github.com/ethpandaops/xatu-data/blob/master/.claude/commands/load-llms-txt.md,,ethpandaops,https://github.com/ethpandaops,TRUE,2025-07-29,2025-05-13:02-44-31,2025-07-29:18-37-31,MIT,"Loads LLM configuration files to context, importing specific terminology, model configurations, and establishing baseline terminology for AI discussions."
cmd-cc5f7cd3,/load_coo_context,Slash-Commands,Context Loading & Priming,https://github.com/Mjvolk3/torchcell/blob/main/.claude/commands/load_coo_context.md,,Mjvolk3,https://github.com/Mjvolk3,TRUE,,2025-05-08:02-33-13,2025-07-29:18-37-32,NOT_FOUND,"References specific files for sparse matrix operations, explains transform usage, compares with previous approaches, and sets data formatting context for development."
cmd-63a682e3,/load_dango_pipeline,Slash-Commands,Context Loading & Priming,https://github.com/Mjvolk3/torchcell/blob/main/.claude/commands/load_dango_pipeline.md,,Mjvolk3,https://github.com/Mjvolk3,TRUE,,2025-05-08:20-13-41,2025-07-29:18-37-33,NOT_FOUND,"Sets context for model training by referencing pipeline files, establishing working context, and preparing for pipeline work with relevant documentation."
cmd-f4c7bb3c,/prime,Slash-Commands,Context Loading & Priming,https://github.com/yzyydev/AI-Engineering-Structure/blob/main/.claude/commands/prime.md,,yzyydev,https://github.com/yzyydev,TRUE,2025-07-29,2025-05-08:11-29-49,2025-07-29:18-37-34,NOT_FOUND,"Sets up initial project context by viewing directory structure and reading key files, creating standardized context with directory visualization and key documentation focus."
cmd-6467d59f,/reminder,Slash-Commands,Context Loading & Priming,https://github.com/cmxela/thinkube/blob/main/.claude/commands/reminder.md,,cmxela,https://github.com/cmxela,FALSE,,,2025-07-29:18-37-34,NOT_FOUND,"Re-establishes project context after conversation breaks or compaction, restoring context and fixing guideline inconsistencies for complex implementations."
cmd-acaa3ecd,/rsi,Slash-Commands,Context Loading & Priming,https://github.com/ddisisto/si/blob/main/.claude/commands/rsi.md,,ddisisto,https://github.com/ddisisto,TRUE,2025-07-29,2025-05-18:02-11-55,2025-07-29:18-37-35,NOT_FOUND,"Reads all commands and key project files to optimize AI-assisted development by streamlining the process, loading command context, and setting up for better development workflow."
cmd-989ec43f,/add-to-changelog,Slash-Commands,Documentation & Changelogs,https://github.com/berrydev-ai/blockdoc-python/blob/main/.claude/commands/add-to-changelog.md,,berrydev-ai,https://github.com/berrydev-ai,TRUE,2025-07-29,2025-04-25:23-48-11,2025-07-29:18-37-35,MIT,"Adds new entries to changelog files while maintaining format consistency, properly documenting changes, and following established project standards for version tracking."
cmd-416793e8,/create-docs,Slash-Commands,Documentation & Changelogs,https://github.com/jerseycheese/Narraitor/tree/feature/issue-227-ai-suggestions/.claude/commands/analyze-issue.md,,jerseycheese,https://github.com/jerseycheese,TRUE,2025-07-29,,2025-07-29:18-37-36,MIT,"Analyzes code structure and purpose to create comprehensive documentation detailing inputs/outputs, behavior, user interaction flows, and edge cases with error handling."
cmd-4d612ab9,/docs,Slash-Commands,Documentation & Changelogs,https://github.com/slunsford/coffee-analytics/blob/main/.claude/commands/docs.md,,slunsford,https://github.com/slunsford,TRUE,2025-07-29,2025-05-27:23-04-05,2025-07-29:18-37-36,NOT_FOUND,"Generates comprehensive documentation that follows project structure, documenting APIs and usage patterns with consistent formatting for better user understanding."
cmd-7c4c3c47,/explain-issue-fix,Slash-Commands,Documentation & Changelogs,https://github.com/hackdays-io/toban-contribution-viewer/blob/main/.claude/commands/explain-issue-fix.md,,hackdays-io,https://github.com/hackdays-io,TRUE,2025-07-29,2025-04-23:07-53-14,2025-07-29:18-37-37,NOT_FOUND,"Documents solution approaches for GitHub issues, explaining technical decisions, detailing challenges overcome, and providing implementation context for better understanding."
cmd-7767f28f,/update-docs,Slash-Commands,Documentation & Changelogs,https://github.com/Consiliency/Flutter-Structurizr/blob/main/.claude/commands/update-docs.md,,Consiliency,https://github.com/Consiliency,TRUE,2025-07-29,2025-05-18:18-20-23,2025-07-29:18-37-38,MIT,"Reviews current documentation status, updates implementation progress, reviews phase documents, and maintains documentation consistency across the project."
cmd-089f917a,/act,Slash-Commands,Miscellaneous,https://github.com/sotayamashita/dotfiles/blob/main/.claude/commands/act.md,,sotayamashita,https://github.com/sotayamashita,FALSE,,2025-06-29:06-25-59,2025-07-29:18-37-38,MIT,"Generates React components with proper accessibility, creating ARIA-compliant components with keyboard navigation that follow React best practices and include comprehensive accessibility testing."
cmd-48cb3d9e,/dump,Slash-Commands,Miscellaneous,https://gist.github.com/fumito-ito/77c308e0382e06a9c16b22619f8a2f83#file-dump-md,,fumito-ito,https://github.com/fumito-ito,FALSE,,,2025-07-29:18-37-38,NOT_FOUND,Dumps the current Claude Code conversation to a markdown file in `.claude/logs/` with timestamped files that include session details and preserve full conversation history.
cmd-6581d11f,/five,Slash-Commands,Miscellaneous,https://github.com/TuckerTucker/tkr-portfolio/blob/main/.claude/commands/five.md,,TuckerTucker,https://github.com/TuckerTucker,TRUE,2025-07-29,2025-06-25:06-46-24,2025-07-29:18-37-39,NOT_FOUND,"Applies the ""five whys"" methodology to perform root cause analysis, identify underlying issues, and create solution approaches for complex problems."
cmd-a0a98a9e,/fixing_go_in_graph,Slash-Commands,Miscellaneous,https://github.com/Mjvolk3/torchcell/blob/main/.claude/commands/fixing_go_in_graph.md,,Mjvolk3,https://github.com/Mjvolk3,TRUE,,2025-05-15:23-13-53,2025-07-29:18-37-40,NOT_FOUND,"Focuses on Gene Ontology annotation integration in graph databases, handling multiple data sources, addressing graph representation issues, and ensuring correct data incorporation."
cmd-40432dca,/mermaid,Slash-Commands,Miscellaneous,https://github.com/GaloyMoney/lana-bank/blob/main/.claude/commands/mermaid.md,,GaloyMoney,https://github.com/GaloyMoney,TRUE,2025-07-29,2025-03-30:17-53-38,2025-07-29:18-37-40,NOASSERTION,"Generates Mermaid diagrams from SQL schema files, creating entity relationship diagrams with table properties, validating diagram compilation, and ensuring complete entity coverage."
cmd-dc2a5edd,/review_dcell_model,Slash-Commands,Miscellaneous,https://github.com/Mjvolk3/torchcell/blob/main/.claude/commands/review_dcell_model.md,,Mjvolk3,https://github.com/Mjvolk3,TRUE,2025-07-29,2025-05-15:23-13-53,2025-07-29:18-37-41,NOT_FOUND,"Reviews old Dcell implementation files, comparing with newer Dango model, noting changes over time, and analyzing refactoring approaches for better code organization."
cmd-0a1fa75a,/use-stepper,Slash-Commands,Miscellaneous,https://github.com/zuplo/docs/blob/main/.claude/commands/use-stepper.md,,zuplo,https://github.com/zuplo,TRUE,2025-07-29,2025-04-19:15-58-21,2025-07-29:18-37-42,NOT_FOUND,"Reformats documentation to use React Stepper component, transforming heading formats, applying proper indentation, and maintaining markdown compatibility with admonition formatting."
cmd-8856ecb4,/create-command,Slash-Commands,Project & Task Management,https://github.com/scopecraft/command/blob/main/.claude/commands/create-command.md,,scopecraft,https://github.com/scopecraft,TRUE,2025-07-29,2025-05-18:02-25-43,2025-07-29:18-37-43,NOT_FOUND,"Guides Claude through creating new custom commands with proper structure by analyzing requirements, templating commands by category, enforcing command standards, and creating supporting documentation."
cmd-15eb4d26,/create-jtbd,Slash-Commands,Project & Task Management,https://github.com/taddyorg/inkverse/blob/main/.claude/commands/create-jtbd.md,,taddyorg,https://github.com/taddyorg,TRUE,,2025-05-15:17-51-07,2025-07-29:18-37-43,AGPL-3.0,"Creates Jobs-to-be-Done frameworks that outline user needs with structured format, focusing on specific user problems and organizing by job categories for product development."
cmd-0420f9cb,/create-prd,Slash-Commands,Project & Task Management,https://github.com/taddyorg/inkverse/blob/main/.claude/commands/create-prd.md,,taddyorg,https://github.com/taddyorg,TRUE,2025-07-29,2025-05-15:17-51-07,2025-07-29:18-37-44,AGPL-3.0,"Generates comprehensive product requirement documents outlining detailed specifications, requirements, and features following standardized document structure and format."
cmd-ec48035a,/create-prp,Slash-Commands,Project & Task Management,https://github.com/Wirasm/claudecode-utils/blob/main/.claude/commands/create-prp.md,,Wirasm,https://github.com/Wirasm,TRUE,2025-07-29,2025-05-15:12-52-18,2025-07-29:18-37-45,MIT,"Creates product requirement plans by reading PRP methodology, following template structure, creating comprehensive requirements, and structuring product definitions for development."
cmd-2981aaf0,/do-issue,Slash-Commands,Project & Task Management,https://github.com/jerseycheese/Narraitor/blob/feature/issue-227-ai-suggestions/.claude/commands/do-issue.md,,jerseycheese,https://github.com/jerseycheese,FALSE,,,2025-07-29:18-37-45,MIT,"Implements GitHub issues with manual review points, following a structured approach with issue number parameter and offering alternative automated mode for efficiency."
cmd-f5425f91,/next-task,Slash-Commands,Project & Task Management,https://github.com/wmjones/wyatt-personal-aws/blob/main/.claude/commands/next-task.md,,wmjones,https://github.com/wmjones,FALSE,,,2025-07-29:18-37-45,NOT_FOUND,"Gets the next task from TaskMaster and creates a branch for it, integrating with task management systems, automating branch creation, and enforcing naming conventions."
cmd-80018864,/project_hello_w_name,Slash-Commands,Project & Task Management,https://github.com/disler/just-prompt/blob/main/.claude/commands/project_hello_w_name.md,,disler,https://github.com/disler,TRUE,2025-07-29,2025-03-21:15-24-08,2025-07-29:18-37-46,NOT_FOUND,"Creates customizable greeting components with name input, demonstrating argument passing, component reusability, state management, and user input handling."
cmd-1bc55517,/todo,Slash-Commands,Project & Task Management,https://github.com/chrisleyva/todo-slash-command/blob/main/todo.md,,chrisleyva,https://github.com/chrisleyva,TRUE,2025-07-29,2025-06-25:23-12-22,2025-07-29:18-37-47,MIT,"A convenient command to quickly manage project todo items without leaving the Claude Code interface, featuring due dates, sorting, task prioritization, and comprehensive todo list management."
cmd-9d234db1,/analyze-issue,Slash-Commands,Version Control & Git,https://github.com/jerseycheese/Narraitor/blob/feature/issue-227-ai-suggestions/.claude/commands/analyze-issue.md,,jerseycheese,https://github.com/jerseycheese,FALSE,,,2025-07-29:18-37-47,MIT,"Fetches GitHub issue details to create comprehensive implementation specifications, analyzing requirements and planning structured approach with clear implementation steps."
cmd-4a72b306,/bug-fix,Slash-Commands,Version Control & Git,https://github.com/danielscholl/mvn-mcp-server/blob/main/.claude/commands/bug-fix.md,,danielscholl,https://github.com/danielscholl,TRUE,2025-07-29,2025-05-06:23-07-03,2025-07-29:18-37-47,NOT_FOUND,"Streamlines bug fixing by creating a GitHub issue first, then a feature branch for implementing and thoroughly testing the solution before merging."
cmd-b6a797df,/commit,Slash-Commands,Version Control & Git,https://github.com/evmts/tevm-monorepo/blob/main/.claude/commands/commit.md,,evmts,https://github.com/evmts,TRUE,,2025-03-25:09-20-57,2025-07-29:18-37-48,MIT,"Creates git commits using conventional commit format with appropriate emojis, following project standards and creating descriptive messages that explain the purpose of changes."
cmd-6aeeadd6,/commit-fast,Slash-Commands,Version Control & Git,https://github.com/steadycursor/steadystart/blob/main/.claude/commands/2-commit-fast.md,,steadycursor,https://github.com/steadycursor,TRUE,,2025-04-04:20-37-25,2025-07-29:18-37-49,NOT_FOUND,"Automates git commit process by selecting the first suggested message, generating structured commits with consistent formatting while skipping manual confirmation and removing Claude co-Contributorship footer"
cmd-2f41bf88,/create-pr,Slash-Commands,Version Control & Git,https://github.com/toyamarinyon/giselle/blob/main/.claude/commands/create-pr.md,,toyamarinyon,https://github.com/toyamarinyon,TRUE,2025-07-29,2025-04-04:03-22-03,2025-07-29:18-37-50,Apache-2.0,"Streamlines pull request creation by handling the entire workflow: creating a new branch, committing changes, formatting modified files with Biome, and submitting the PR."
cmd-6f066b19,/create-pull-request,Slash-Commands,Version Control & Git,https://github.com/liam-hq/liam/blob/main/.claude/commands/create-pull-request.md,,liam-hq,https://github.com/liam-hq,TRUE,2025-07-29,2025-07-11:08-28-12,2025-07-29:18-37-50,Apache-2.0,"Provides comprehensive PR creation guidance with GitHub CLI, enforcing title conventions, following template structure, and offering concrete command examples with best practices."
cmd-54c60a04,/create-worktrees,Slash-Commands,Version Control & Git,https://github.com/evmts/tevm-monorepo/blob/main/.claude/commands/create-worktrees.md,,evmts,https://github.com/evmts,TRUE,,2025-03-14:02-00-50,2025-07-29:18-37-51,MIT,"Creates git worktrees for all open PRs or specific branches, handling branches with slashes, cleaning up stale worktrees, and supporting custom branch creation for development."
cmd-d39b623d,/fix-github-issue,Slash-Commands,Version Control & Git,https://github.com/jeremymailen/kotlinter-gradle/blob/master/.claude/commands/fix-github-issue.md,,jeremymailen,https://github.com/jeremymailen,TRUE,2025-07-29,2025-04-24:05-58-40,2025-07-29:18-37-52,Apache-2.0,"Analyzes and fixes GitHub issues using a structured approach with GitHub CLI for issue details, implementing necessary code changes, running tests, and creating proper commit messages."
cmd-85f39721,/fix-issue,Slash-Commands,Version Control & Git,https://github.com/metabase/metabase/blob/master/.claude/commands/fix-issue.md,,metabase,https://github.com/metabase,TRUE,,2025-04-08:08-37-04,2025-07-29:18-37-53,NOASSERTION,"Addresses GitHub issues by taking issue number as parameter, analyzing context, implementing solution, and testing/validating the fix for proper integration."
cmd-16c71a8c,/fix-pr,Slash-Commands,Version Control & Git,https://github.com/metabase/metabase/blob/master/.claude/commands/fix-pr.md,,metabase,https://github.com/metabase,TRUE,,2025-04-08:08-37-04,2025-07-29:18-37-54,NOASSERTION,"Fetches and fixes unresolved PR comments by automatically retrieving feedback, addressing reviewer concerns, making targeted code improvements, and streamlining the review process."
cmd-a1042630,/husky,Slash-Commands,Version Control & Git,https://github.com/evmts/tevm-monorepo/blob/main/.claude/commands/husky.md,,evmts,https://github.com/evmts,TRUE,2025-07-29,2025-03-07:19-38-27,2025-07-29:18-37-55,MIT,"Sets up and manages Husky Git hooks by configuring pre-commit hooks, establishing commit message standards, integrating with linting tools, and ensuring code quality on commits."
cmd-7f51ad4d,/pr-review,Slash-Commands,Version Control & Git,https://github.com/arkavo-org/opentdf-rs/blob/main/.claude/commands/pr-review.md,,arkavo-org,https://github.com/arkavo-org,TRUE,2025-07-29,2025-04-03:01-10-13,2025-07-29:18-37-55,MIT,"Reviews pull request changes to provide feedback, check for issues, and suggest improvements before merging into the main codebase."
cmd-d32f827c,/update-branch-name,Slash-Commands,Version Control & Git,https://github.com/giselles-ai/giselle/blob/main/.claude/commands/update-branch-name.md,,giselles-ai,https://github.com/giselles-ai,TRUE,,2025-04-16:04-08-41,2025-07-29:18-37-56,Apache-2.0,"Updates branch names with proper prefixes and formats, enforcing naming conventions, supporting semantic prefixes, and managing remote branch updates."
claude-6348c9dd,AVS Vibe Developer Guide,CLAUDE.md Files,Domain-Specific,https://github.com/Layr-Labs/avs-vibe-developer-guide/blob/master/CLAUDE.md,,Layr-Labs,https://github.com/Layr-Labs,TRUE,2025-07-29,2025-04-09:18-03-54,2025-07-29:18-37-57,MIT,Structures AI-assisted EigenLayer AVS development workflow with consistent naming conventions for prompt files and established terminology standards for blockchain concepts.
claude-d8f940fa,Comm,CLAUDE.md Files,Domain-Specific,https://github.com/CommE2E/comm/blob/master/CLAUDE.md,,CommE2E,https://github.com/CommE2E,TRUE,2025-07-29,2025-03-21:02-22-09,2025-07-29:18-37-58,BSD-3-Clause,"Serves as a development reference for E2E-encrypted messaging applications with code organization architecture, security implementation details, and testing procedures."
claude-d0e5c826,Course Builder,CLAUDE.md Files,Domain-Specific,https://github.com/badass-courses/course-builder/blob/main/CLAUDE.md,,badass-courses,https://github.com/badass-courses,TRUE,2025-07-29,2025-03-18:02-42-14,2025-07-29:18-37-58,MIT,Enables real-time multiplayer capabilities for collaborative course creation with diverse tech stack integration and monorepo architecture using Turborepo.
claude-3b207e6e,Cursor Tools,CLAUDE.md Files,Domain-Specific,https://github.com/eastlondoner/cursor-tools/blob/main/CLAUDE.md,,eastlondoner,https://github.com/eastlondoner,TRUE,2025-07-29,2025-04-26:20-26-53,2025-07-29:18-37-59,MIT,"Creates a versatile AI command interface supporting multiple providers and models with flexible command options and browser automation through ""Stagehand"" feature."
claude-0ce42e78,Guitar,CLAUDE.md Files,Domain-Specific,https://github.com/soramimi/Guitar/blob/master/CLAUDE.md,,soramimi,https://github.com/soramimi,TRUE,2025-07-29,2025-03-28:11-01-30,2025-07-29:18-38-00,GPL-2.0,"Serves as development guide for Guitar Git GUI Client with build commands for various platforms, code style guidelines for contributing, and project structure explanation."
claude-4a956e32,Network Chronicles,CLAUDE.md Files,Domain-Specific,https://github.com/Fimeg/NetworkChronicles/blob/legacy-v1/CLAUDE.md,,Fimeg,https://github.com/Fimeg,TRUE,2025-07-29,,2025-07-29:18-38-01,MIT,"Presents detailed implementation plan for AI-driven game characters with technical specifications for LLM integration, character guidelines, and service discovery mechanics."
claude-d97bf254,Note Companion,CLAUDE.md Files,Domain-Specific,https://github.com/different-ai/note-companion/blob/master/CLAUDE.md,,different-ai,https://github.com/different-ai,TRUE,2025-07-29,2025-03-11:08-16-20,2025-07-29:18-38-02,MIT,Provides detailed styling isolation techniques for Obsidian plugins using Tailwind with custom prefix to prevent style conflicts and practical troubleshooting steps.
claude-5479b4e8,Pareto Mac,CLAUDE.md Files,Domain-Specific,https://github.com/ParetoSecurity/pareto-mac/blob/main/CLAUDE.md,,ParetoSecurity,https://github.com/ParetoSecurity,TRUE,2025-07-29,2025-06-20:14-31-59,2025-07-29:18-38-02,GPL-3.0,"Serves as development guide for Mac security audit tool with build instructions, contribution guidelines, testing procedures, and workflow documentation."
claude-2659fc4a,SteadyStart,CLAUDE.md Files,Domain-Specific,https://github.com/steadycursor/steadystart/blob/main/CLAUDE.md,,steadycursor,https://github.com/steadycursor,TRUE,2025-07-29,2025-05-12:14-41-24,2025-07-29:18-38-03,NOT_FOUND,"Clear and direct instructives about style, permissions, Claude's ""role"", communications, and documentation of Claude Code sessions for other team members to stay abreast."
claude-ac32c909,AI IntelliJ Plugin,CLAUDE.md Files,Language-Specific,https://github.com/didalgolab/ai-intellij-plugin/blob/main/CLAUDE.md,,didalgolab,https://github.com/didalgolab,TRUE,2025-07-29,2025-03-06:19-12-46,2025-07-29:18-38-04,Apache-2.0,"Provides comprehensive Gradle commands for IntelliJ plugin development with platform-specific coding patterns, detailed package structure guidelines, and clear internationalization standards."
claude-bbaa0c15,AWS MCP Server,CLAUDE.md Files,Language-Specific,https://github.com/alexei-led/aws-mcp-server/blob/main/CLAUDE.md,,alexei-led,https://github.com/alexei-led,TRUE,2025-07-29,2025-04-06:11-57-11,2025-07-29:18-38-04,MIT,"Features multiple Python environment setup options with detailed code style guidelines, comprehensive error handling recommendations, and security considerations for AWS CLI interactions."
claude-e130a9c3,DroidconKotlin,CLAUDE.md Files,Language-Specific,https://github.com/touchlab/DroidconKotlin/blob/main/CLAUDE.md,,touchlab,https://github.com/touchlab,TRUE,2025-07-29,2025-03-26:03-16-04,2025-07-29:18-38-05,Apache-2.0,Delivers comprehensive Gradle commands for cross-platform Kotlin Multiplatform development with clear module structure and practical guidance for dependency injection.
claude-1279cf13,EDSL,CLAUDE.md Files,Language-Specific,https://github.com/expectedparrot/edsl/blob/main/CLAUDE.md,,expectedparrot,https://github.com/expectedparrot,TRUE,2025-07-29,2025-07-11:13-39-20,2025-07-29:18-38-06,MIT,"Offers detailed build and test commands with strict code style enforcement, comprehensive testing requirements, and standardized development workflow using Black and mypy."
claude-3ae444b3,Giselle,CLAUDE.md Files,Language-Specific,https://github.com/giselles-ai/giselle/blob/main/CLAUDE.md,,giselles-ai,https://github.com/giselles-ai,TRUE,2025-07-29,2025-05-23:03-56-37,2025-07-29:18-38-07,Apache-2.0,Provides detailed build and test commands using pnpm and Vitest with strict code formatting requirements and comprehensive naming conventions for code consistency.
claude-b302b042,HASH,CLAUDE.md Files,Language-Specific,https://github.com/hashintel/hash/blob/main/CLAUDE.md,,hashintel,https://github.com/hashintel,TRUE,2025-07-29,2025-07-01:08-29-46,2025-07-29:18-38-08,NOASSERTION,"Features comprehensive repository structure breakdown with strong emphasis on coding standards, detailed Rust documentation guidelines, and systematic PR review process."
claude-6dc32b06,Inkline,CLAUDE.md Files,Language-Specific,https://github.com/inkline/inkline/blob/main/CLAUDE.md,,inkline,https://github.com/inkline,TRUE,2025-07-29,2025-03-03:21-25-46,2025-07-29:18-38-08,NOASSERTION,"Structures development workflow using pnpm with emphasis on TypeScript and Vue 3 Composition API, detailed component creation process, and comprehensive testing recommendations."
claude-1821727a,JSBeeb,CLAUDE.md Files,Language-Specific,https://github.com/mattgodbolt/jsbeeb/blob/main/CLAUDE.md,,mattgodbolt,https://github.com/mattgodbolt,TRUE,2025-07-29,2025-07-02:04-46-25,2025-07-29:18-38-09,GPL-3.0,"Provides development guide for JavaScript BBC Micro emulator with build and testing instructions, architecture documentation, and debugging workflows."
claude-3591a3e4,Lamoom Python,CLAUDE.md Files,Language-Specific,https://github.com/LamoomAI/lamoom-python/blob/main/CLAUDE.md,,LamoomAI,https://github.com/LamoomAI,TRUE,2025-07-29,2025-03-05:21-42-33,2025-07-29:18-38-10,Apache-2.0,"Serves as reference for production prompt engineering library with load balancing of AI Models, API documentation, and usage patterns with examples."
claude-2a18266c,LangGraphJS,CLAUDE.md Files,Language-Specific,https://github.com/langchain-ai/langgraphjs/blob/main/CLAUDE.md,,langchain-ai,https://github.com/langchain-ai,TRUE,2025-07-29,2025-03-10:03-51-26,2025-07-29:18-38-11,MIT,"Offers comprehensive build and test commands with detailed TypeScript style guidelines, layered library architecture, and monorepo structure using yarn workspaces."
claude-38b6b458,Metabase,CLAUDE.md Files,Language-Specific,https://github.com/metabase/metabase/blob/master/CLAUDE.md,,metabase,https://github.com/metabase,TRUE,2025-07-29,2025-07-09:23-09-17,2025-07-29:18-38-12,NOASSERTION,"Details workflow for REPL-driven development in Clojure/ClojureScript with emphasis on incremental development, testing, and step-by-step approach for feature implementation."
claude-8ff859d0,SG Cars Trends Backend,CLAUDE.md Files,Language-Specific,https://github.com/sgcarstrends/backend/blob/main/CLAUDE.md,,sgcarstrends,https://github.com/sgcarstrends,TRUE,2025-07-29,2025-06-18:04-20-45,2025-07-29:18-38-13,NOT_FOUND,"Provides comprehensive structure for TypeScript monorepo projects with detailed commands for development, testing, deployment, and AWS/Cloudflare integration."
claude-28de7758,SPy,CLAUDE.md Files,Language-Specific,https://github.com/spylang/spy/blob/main/CLAUDE.md,,spylang,https://github.com/spylang,TRUE,2025-07-29,2025-03-16:15-20-50,2025-07-29:18-38-14,MIT,"Enforces strict coding conventions with comprehensive testing guidelines, multiple code compilation options, and backend-specific test decorators for targeted filtering."
claude-724817c4,TPL,CLAUDE.md Files,Language-Specific,https://github.com/KarpelesLab/tpl/blob/master/CLAUDE.md,,KarpelesLab,https://github.com/KarpelesLab,TRUE,2025-07-29,2025-03-28:07-35-50,2025-07-29:18-38-14,MIT,"Details Go project conventions with comprehensive error handling recommendations, table-driven testing approach guidelines, and modernization suggestions for latest Go features."
claude-14f59511,Basic Memory,CLAUDE.md Files,Project Scaffolding & MCP,https://github.com/basicmachines-co/basic-memory/blob/main/CLAUDE.md,,basicmachines-co,https://github.com/basicmachines-co,TRUE,2025-07-29,2025-06-21:13-12-23,2025-07-29:18-38-15,AGPL-3.0,Presents an innovative AI-human collaboration framework with Model Context Protocol for bidirectional LLM-markdown communication and flexible knowledge structure for complex projects.
claude-65aa541a,claude-code-mcp-enhanced,CLAUDE.md Files,Project Scaffolding & MCP,https://github.com/grahama1970/claude-code-mcp-enhanced/blob/main/CLAUDE.md,,grahama1970,https://github.com/grahama1970,TRUE,2025-07-29,2025-05-16:14-52-36,2025-07-29:18-38-16,MIT,"Provides detailed and emphatic instructions for Claude to follow as a coding agent, with testing guidance, code examples, and compliance checks."
claude-36517eea,MCP Engine,CLAUDE.md Files,Project Scaffolding & MCP,https://github.com/featureform/mcp-engine/blob/main/CLAUDE.md,,featureform,https://github.com/featureform,FALSE,,,2025-07-29:18-38-16,Apache-2.0,"Enforces strict package management with comprehensive type checking rules, explicit PR description guidelines, and systematic approach to resolving CI failures."
claude-4a53c9e8,Perplexity MCP,CLAUDE.md Files,Project Scaffolding & MCP,https://github.com/Family-IT-Guy/perplexity-mcp/blob/main/CLAUDE.md,,Family-IT-Guy,https://github.com/Family-IT-Guy,TRUE,2025-07-29,2025-03-20:04-04-46,2025-07-29:18-38-17,ISC,"Offers clear step-by-step installation instructions with multiple configuration options, detailed troubleshooting guidance, and concise architecture overview of the MCP protocol."
doc-93f22142,Anthropic Documentation,Official Documentation,,https://docs.anthropic.com/en/docs/claude-code,,Anthropic,https://github.com/anthropics,TRUE,,,2025-07-29:18-38-17,&copy;,"The official documentation for Claude Code, including installation instructions, usage guidelines, API references, tutorials, examples, loads of information that I won't list individually. Like Claude Code, the documentation is frequently updated."
doc-b71240b4,Anthropic Quickstarts,Official Documentation,,https://github.com/anthropics/anthropic-quickstarts/blob/main/CLAUDE.md,,Anthropic,https://github.com/anthropics,TRUE,2025-07-29,2025-02-24:19-02-28,2025-07-29:18-38-18,MIT,"Offers comprehensive development guides for three distinct AI-powered demo projects with standardized workflows, strict code style guidelines, and containerization instructions."
doc-9703ea36,Claude Code GitHub Actions,Official Documentation,,https://github.com/anthropics/claude-code-action/tree/main/examples,,Anthropic,https://github.com/anthropics,TRUE,2025-07-29,,2025-07-29:18-38-18,MIT,Official GitHub Actions integration for Claude Code with examples and documentation for automating AI-powered workflows in CI/CD pipelines.
