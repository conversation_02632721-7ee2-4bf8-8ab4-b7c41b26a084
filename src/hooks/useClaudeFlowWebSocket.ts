/**
 * React Hook for Claude Flow WebSocket Integration
 * Provides real-time updates and state management
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { Claude<PERSON>lowWebSocket, <PERSON><PERSON><PERSON>Event, ClaudeFlowEventType, createClaudeFlowWebSocket } from '@/lib/claudeFlowWebSocket';
import { ClaudeFlowAgent, ClaudeFlowTask, ClaudeFlowMemoryEntry, ClaudeFlowSystemStatus } from '@/lib/claudeFlowIntegration';

export interface UseClaudeFlowWebSocketOptions {
  host?: string;
  port?: number;
  autoConnect?: boolean;
  onConnect?: () => void;
  onDisconnect?: (reason: string) => void;
  onError?: (error: Error) => void;
}

export interface ClaudeFlowWebSocketState {
  connected: boolean;
  connecting: boolean;
  error: Error | null;
  lastEvent: ClaudeFlowEvent | null;
}

export interface ClaudeFlowWebSocketActions {
  connect: () => Promise<void>;
  disconnect: () => void;
  subscribe: (eventType: ClaudeFlowEventType, handler: (event: <PERSON><PERSON><PERSON><PERSON><PERSON>) => void) => () => void;
  send: (message: any) => void;
}

export function useClaude<PERSON>lowWebSocket(options: UseClaudeFlowWebSocketOptions = {}) {
  const [state, setState] = useState<ClaudeFlowWebSocketState>({
    connected: false,
    connecting: false,
    error: null,
    lastEvent: null
  });

  const wsRef = useRef<ClaudeFlowWebSocket | null>(null);
  const handlersRef = useRef<{
    onConnect?: () => void;
    onDisconnect?: (reason: string) => void;
    onError?: (error: Error) => void;
  }>({});

  // Update handlers ref
  handlersRef.current = {
    onConnect: options.onConnect,
    onDisconnect: options.onDisconnect,
    onError: options.onError
  };

  // Initialize WebSocket
  useEffect(() => {
    if (!wsRef.current) {
      wsRef.current = createClaudeFlowWebSocket({
        host: options.host || 'localhost',
        port: options.port || 8765
      });

      // Set up connection handlers
      wsRef.current.onConnect(() => {
        setState(prev => ({ ...prev, connected: true, connecting: false, error: null }));
        handlersRef.current.onConnect?.();
      });

      wsRef.current.onDisconnect((reason) => {
        setState(prev => ({ ...prev, connected: false, connecting: false }));
        handlersRef.current.onDisconnect?.(reason);
      });

      wsRef.current.onError((error) => {
        setState(prev => ({ ...prev, error, connecting: false }));
        handlersRef.current.onError?.(error);
      });
    }

    // Auto-connect if enabled
    if (options.autoConnect !== false) {
      connect();
    }

    // Cleanup on unmount
    return () => {
      if (wsRef.current) {
        wsRef.current.disconnect();
        wsRef.current = null;
      }
    };
  }, []); // Only run on mount

  const connect = useCallback(async () => {
    if (!wsRef.current || state.connected || state.connecting) {
      return;
    }

    setState(prev => ({ ...prev, connecting: true, error: null }));

    try {
      await wsRef.current.connect();
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        connecting: false, 
        error: error instanceof Error ? error : new Error('Failed to connect') 
      }));
    }
  }, [state.connected, state.connecting]);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.disconnect();
    }
  }, []);

  const subscribe = useCallback((eventType: ClaudeFlowEventType, handler: (event: ClaudeFlowEvent) => void) => {
    if (!wsRef.current) {
      throw new Error('WebSocket not initialized');
    }

    // Also update last event in state
    const wrappedHandler = (event: ClaudeFlowEvent) => {
      setState(prev => ({ ...prev, lastEvent: event }));
      handler(event);
    };

    return wsRef.current.on(eventType, wrappedHandler);
  }, []);

  const send = useCallback((message: any) => {
    if (!wsRef.current) {
      throw new Error('WebSocket not initialized');
    }

    wsRef.current.send(message);
  }, []);

  const actions: ClaudeFlowWebSocketActions = {
    connect,
    disconnect,
    subscribe,
    send
  };

  return { state, actions };
}

/**
 * Hook for subscribing to specific Claude Flow events
 */
export function useClaudeFlowEvent<T = any>(
  eventType: ClaudeFlowEventType,
  handler: (data: T) => void,
  deps: React.DependencyList = []
) {
  const { actions } = useClaudeFlowWebSocket();

  useEffect(() => {
    const unsubscribe = actions.subscribe(eventType, (event) => {
      handler(event.data as T);
    });

    return unsubscribe;
  }, deps);
}

/**
 * Hook for real-time agent updates
 */
export function useClaudeFlowAgentUpdates(
  onAgentUpdate: (agent: ClaudeFlowAgent, eventType: ClaudeFlowEventType) => void
) {
  const { actions } = useClaudeFlowWebSocket();

  useEffect(() => {
    const unsubscribers: (() => void)[] = [];

    // Subscribe to agent events
    unsubscribers.push(
      actions.subscribe('agent.spawned', (event) => {
        onAgentUpdate(event.data as ClaudeFlowAgent, event.type);
      })
    );

    unsubscribers.push(
      actions.subscribe('agent.updated', (event) => {
        onAgentUpdate(event.data as ClaudeFlowAgent, event.type);
      })
    );

    unsubscribers.push(
      actions.subscribe('agent.terminated', (event) => {
        onAgentUpdate(event.data as ClaudeFlowAgent, event.type);
      })
    );

    return () => {
      unsubscribers.forEach(unsub => unsub());
    };
  }, [actions, onAgentUpdate]);
}

/**
 * Hook for real-time task updates
 */
export function useClaudeFlowTaskUpdates(
  onTaskUpdate: (task: ClaudeFlowTask, eventType: ClaudeFlowEventType) => void
) {
  const { actions } = useClaudeFlowWebSocket();

  useEffect(() => {
    const unsubscribers: (() => void)[] = [];

    // Subscribe to task events
    const taskEvents: ClaudeFlowEventType[] = [
      'task.created',
      'task.assigned',
      'task.updated',
      'task.completed',
      'task.failed',
      'task.cancelled'
    ];

    taskEvents.forEach(eventType => {
      unsubscribers.push(
        actions.subscribe(eventType, (event) => {
          onTaskUpdate(event.data as ClaudeFlowTask, event.type);
        })
      );
    });

    return () => {
      unsubscribers.forEach(unsub => unsub());
    };
  }, [actions, onTaskUpdate]);
}

/**
 * Hook for real-time system status updates
 */
export function useClaudeFlowSystemStatus(
  onStatusUpdate: (status: ClaudeFlowSystemStatus) => void
) {
  useClaudeFlowEvent<ClaudeFlowSystemStatus>('system.status', onStatusUpdate);
}

/**
 * Hook for workflow progress updates
 */
export function useClaudeFlowWorkflowUpdates(
  onWorkflowUpdate: (data: any, eventType: ClaudeFlowEventType) => void
) {
  const { actions } = useClaudeFlowWebSocket();

  useEffect(() => {
    const unsubscribers: (() => void)[] = [];

    // Subscribe to workflow events
    const workflowEvents: ClaudeFlowEventType[] = [
      'workflow.started',
      'workflow.phase.completed',
      'workflow.completed',
      'workflow.failed'
    ];

    workflowEvents.forEach(eventType => {
      unsubscribers.push(
        actions.subscribe(eventType, (event) => {
          onWorkflowUpdate(event.data, event.type);
        })
      );
    });

    return () => {
      unsubscribers.forEach(unsub => unsub());
    };
  }, [actions, onWorkflowUpdate]);
}