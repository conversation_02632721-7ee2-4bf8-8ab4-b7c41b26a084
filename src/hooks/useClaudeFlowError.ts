import { useState, useCallback, useEffect } from 'react';
import { claudeFlowErrorRecovery } from '@/components/ClaudeFlowErrorBoundary';

export interface ClaudeFlowError {
  type: 'connection' | 'agent' | 'task' | 'memory' | 'general';
  message: string;
  details?: any;
  timestamp: Date;
  retryCount: number;
  recoverable: boolean;
}

interface UseClaudeFlowErrorOptions {
  maxRetries?: number;
  retryDelay?: number;
  onError?: (error: ClaudeFlowError) => void;
  onRecovery?: () => void;
}

export const useClaudeFlowError = (options: UseClaudeFlowErrorOptions = {}) => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    onError,
    onRecovery
  } = options;

  const [errors, setErrors] = useState<ClaudeFlowError[]>([]);
  const [isRecovering, setIsRecovering] = useState(false);

  // Add a new error
  const addError = useCallback((
    type: <PERSON><PERSON>lowError['type'],
    message: string,
    details?: any,
    recoverable = true
  ) => {
    const newError: ClaudeFlowError = {
      type,
      message,
      details,
      timestamp: new Date(),
      retryCount: 0,
      recoverable
    };

    setErrors(prev => [...prev, newError]);
    
    if (onError) {
      onError(newError);
    }

    // Log to console
    console.error(`Claude Flow ${type} error:`, message, details);

    return newError;
  }, [onError]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  // Clear specific error
  const clearError = useCallback((index: number) => {
    setErrors(prev => prev.filter((_, i) => i !== index));
  }, []);

  // Retry with error recovery
  const retryWithRecovery = useCallback(async (
    error: ClaudeFlowError,
    retryAction: () => Promise<void>
  ) => {
    if (!error.recoverable || error.retryCount >= maxRetries) {
      console.error('Error is not recoverable or max retries reached');
      return false;
    }

    setIsRecovering(true);

    try {
      // Apply recovery strategy based on error type
      switch (error.type) {
        case 'connection':
          claudeFlowErrorRecovery.connectionError();
          break;
        case 'agent':
          if (error.details?.agentId) {
            claudeFlowErrorRecovery.agentError(error.details.agentId);
          }
          break;
        case 'task':
          if (error.details?.taskId) {
            claudeFlowErrorRecovery.taskError(error.details.taskId);
          }
          break;
        case 'memory':
          claudeFlowErrorRecovery.memoryError();
          break;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, retryDelay * (error.retryCount + 1)));

      // Attempt the retry action
      await retryAction();

      // If successful, remove the error
      setErrors(prev => prev.filter(e => e !== error));
      
      if (onRecovery) {
        onRecovery();
      }

      return true;
    } catch (retryError) {
      // Update retry count
      setErrors(prev => prev.map(e => 
        e === error 
          ? { ...e, retryCount: e.retryCount + 1 }
          : e
      ));

      console.error('Retry failed:', retryError);
      return false;
    } finally {
      setIsRecovering(false);
    }
  }, [maxRetries, retryDelay, onRecovery]);

  // Handle async operations with error catching
  const handleAsync = useCallback(async <T,>(
    operation: () => Promise<T>,
    errorType: ClaudeFlowError['type'] = 'general',
    errorMessage?: string
  ): Promise<T | undefined> => {
    try {
      return await operation();
    } catch (error) {
      const message = errorMessage || (error instanceof Error ? error.message : 'Unknown error');
      addError(errorType, message, error);
      throw error;
    }
  }, [addError]);

  // Get error summary
  const getErrorSummary = useCallback(() => {
    const summary = {
      total: errors.length,
      byType: {} as Record<ClaudeFlowError['type'], number>,
      recoverable: errors.filter(e => e.recoverable).length,
      recent: errors.slice(-5)
    };

    errors.forEach(error => {
      summary.byType[error.type] = (summary.byType[error.type] || 0) + 1;
    });

    return summary;
  }, [errors]);

  // Auto-clear old errors (older than 5 minutes)
  useEffect(() => {
    const interval = setInterval(() => {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      setErrors(prev => prev.filter(error => error.timestamp > fiveMinutesAgo));
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, []);

  return {
    errors,
    isRecovering,
    addError,
    clearErrors,
    clearError,
    retryWithRecovery,
    handleAsync,
    getErrorSummary,
    hasErrors: errors.length > 0,
    latestError: errors[errors.length - 1]
  };
};

/**
 * Error types for Claude Flow operations
 */
export const ClaudeFlowErrorTypes = {
  CONNECTION_FAILED: 'Failed to connect to Claude Flow server',
  CONNECTION_LOST: 'Lost connection to Claude Flow server',
  AGENT_SPAWN_FAILED: 'Failed to spawn agent',
  AGENT_TERMINATED: 'Agent terminated unexpectedly',
  TASK_CREATE_FAILED: 'Failed to create task',
  TASK_EXECUTION_FAILED: 'Task execution failed',
  MEMORY_STORE_FAILED: 'Failed to store memory entry',
  MEMORY_QUERY_FAILED: 'Failed to query memory',
  CONFIG_SAVE_FAILED: 'Failed to save configuration',
  CONFIG_LOAD_FAILED: 'Failed to load configuration',
  WEBSOCKET_ERROR: 'WebSocket connection error',
  TIMEOUT_ERROR: 'Operation timed out',
  VALIDATION_ERROR: 'Validation error',
  PERMISSION_ERROR: 'Permission denied',
  UNKNOWN_ERROR: 'Unknown error occurred'
} as const;