import { useEffect, useCallback, useRef } from 'react';
import { useClaudeFlowError } from './useClaudeFlowError';

export interface ClaudeFlowKeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  action: () => void | Promise<void>;
  description: string;
  category: 'navigation' | 'agents' | 'tasks' | 'monitoring' | 'general';
  disabled?: boolean;
}

export interface ClaudeFlowKeyboardActions {
  // Navigation shortcuts
  switchToOverview: () => void;
  switchToAgents: () => void;
  switchToTasks: () => void;
  switchToWorkflows: () => void;
  switchToMemory: () => void;
  switchToMonitoring: () => void;
  switchToTerminal: () => void;
  
  // Agent shortcuts
  spawnAgent: () => void;
  refreshAgents: () => void;
  
  // Task shortcuts
  createTask: () => void;
  refreshTasks: () => void;
  
  // General shortcuts
  refreshAll: () => void;
  showHelp: () => void;
  toggleFullscreen: () => void;
  focusSearch: () => void;
}

interface UseClaudeFlowKeyboardShortcutsOptions {
  enabled?: boolean;
  actions: ClaudeFlowKeyboardActions;
  onShortcutTriggered?: (shortcut: ClaudeFlowKeyboardShortcut) => void;
}

/**
 * Hook for managing Claude Flow keyboard shortcuts
 * Provides comprehensive keyboard navigation and operation shortcuts
 */
export const useClaudeFlowKeyboardShortcuts = (options: UseClaudeFlowKeyboardShortcutsOptions) => {
  const { enabled = true, actions, onShortcutTriggered } = options;
  const { handleAsync, addError } = useClaudeFlowError();
  const shortcutsRef = useRef<ClaudeFlowKeyboardShortcut[]>([]);
  const isEnabledRef = useRef(enabled);

  // Update enabled state
  useEffect(() => {
    isEnabledRef.current = enabled;
  }, [enabled]);

  // Define all shortcuts
  const shortcuts: ClaudeFlowKeyboardShortcut[] = [
    // Navigation shortcuts
    {
      key: '1',
      ctrlKey: true,
      action: actions.switchToOverview,
      description: 'Switch to Overview tab',
      category: 'navigation'
    },
    {
      key: '2',
      ctrlKey: true,
      action: actions.switchToAgents,
      description: 'Switch to Agents tab',
      category: 'navigation'
    },
    {
      key: '3',
      ctrlKey: true,
      action: actions.switchToTasks,
      description: 'Switch to Tasks tab',
      category: 'navigation'
    },
    {
      key: '4',
      ctrlKey: true,
      action: actions.switchToWorkflows,
      description: 'Switch to Workflows tab',
      category: 'navigation'
    },
    {
      key: '5',
      ctrlKey: true,
      action: actions.switchToMemory,
      description: 'Switch to Memory tab',
      category: 'navigation'
    },
    {
      key: '6',
      ctrlKey: true,
      action: actions.switchToMonitoring,
      description: 'Switch to Monitoring tab',
      category: 'navigation'
    },
    {
      key: '7',
      ctrlKey: true,
      action: actions.switchToTerminal,
      description: 'Switch to Terminal tab',
      category: 'navigation'
    },

    // Agent shortcuts
    {
      key: 'n',
      ctrlKey: true,
      shiftKey: true,
      action: actions.spawnAgent,
      description: 'Spawn new agent',
      category: 'agents'
    },
    {
      key: 'a',
      ctrlKey: true,
      action: actions.refreshAgents,
      description: 'Refresh agents list',
      category: 'agents'
    },

    // Task shortcuts
    {
      key: 't',
      ctrlKey: true,
      shiftKey: true,
      action: actions.createTask,
      description: 'Create new task',
      category: 'tasks'
    },
    {
      key: 't',
      ctrlKey: true,
      action: actions.refreshTasks,
      description: 'Refresh tasks list',
      category: 'tasks'
    },

    // General shortcuts
    {
      key: 'r',
      ctrlKey: true,
      action: actions.refreshAll,
      description: 'Refresh all data',
      category: 'general'
    },
    {
      key: 'h',
      ctrlKey: true,
      action: actions.showHelp,
      description: 'Show keyboard shortcuts help',
      category: 'general'
    },
    {
      key: 'f',
      ctrlKey: true,
      shiftKey: true,
      action: actions.toggleFullscreen,
      description: 'Toggle fullscreen mode',
      category: 'general'
    },
    {
      key: 'k',
      ctrlKey: true,
      action: actions.focusSearch,
      description: 'Focus search input',
      category: 'general'
    },
    {
      key: '/',
      action: actions.focusSearch,
      description: 'Focus search input (alternative)',
      category: 'general'
    },

    // Quick navigation with arrow keys
    {
      key: 'ArrowLeft',
      altKey: true,
      action: () => {
        // Navigate to previous tab
        const tabs = ['overview', 'agents', 'tasks', 'workflows', 'memory', 'monitoring', 'terminal'];
        const currentTab = document.querySelector('[data-state="active"]')?.getAttribute('data-value');
        if (currentTab) {
          const currentIndex = tabs.indexOf(currentTab);
          const prevIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
          const prevTab = tabs[prevIndex];
          document.querySelector(`[data-value="${prevTab}"]`)?.click();
        }
      },
      description: 'Navigate to previous tab',
      category: 'navigation'
    },
    {
      key: 'ArrowRight',
      altKey: true,
      action: () => {
        // Navigate to next tab
        const tabs = ['overview', 'agents', 'tasks', 'workflows', 'memory', 'monitoring', 'terminal'];
        const currentTab = document.querySelector('[data-state="active"]')?.getAttribute('data-value');
        if (currentTab) {
          const currentIndex = tabs.indexOf(currentTab);
          const nextIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
          const nextTab = tabs[nextIndex];
          document.querySelector(`[data-value="${nextTab}"]`)?.click();
        }
      },
      description: 'Navigate to next tab',
      category: 'navigation'
    }
  ];

  shortcutsRef.current = shortcuts;

  // Check if a keyboard event matches a shortcut
  const matchesShortcut = useCallback((event: KeyboardEvent, shortcut: ClaudeFlowKeyboardShortcut): boolean => {
    const { key, ctrlKey = false, altKey = false, shiftKey = false, metaKey = false } = shortcut;
    
    return (
      event.key === key &&
      event.ctrlKey === ctrlKey &&
      event.altKey === altKey &&
      event.shiftKey === shiftKey &&
      event.metaKey === metaKey
    );
  }, []);

  // Handle keyboard events
  const handleKeyDown = useCallback(async (event: KeyboardEvent) => {
    if (!isEnabledRef.current) return;

    // Don't trigger shortcuts when typing in inputs
    const target = event.target as HTMLElement;
    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true' ||
      target.closest('[contenteditable="true"]')
    ) {
      // Allow Ctrl+K and / to work even in inputs for search
      if (!(
        (event.key === 'k' && event.ctrlKey) ||
        (event.key === '/' && !event.ctrlKey && !event.altKey && !event.shiftKey)
      )) {
        return;
      }
    }

    // Find matching shortcut
    const matchingShortcut = shortcutsRef.current.find(shortcut => 
      !shortcut.disabled && matchesShortcut(event, shortcut)
    );

    if (matchingShortcut) {
      event.preventDefault();
      event.stopPropagation();

      try {
        // Execute the action with error handling
        await handleAsync(
          () => Promise.resolve(matchingShortcut.action()),
          'general',
          `Failed to execute shortcut: ${matchingShortcut.description}`
        );

        // Notify that shortcut was triggered
        if (onShortcutTriggered) {
          onShortcutTriggered(matchingShortcut);
        }
      } catch (error) {
        console.error('Shortcut execution failed:', error);
        addError('general', `Keyboard shortcut failed: ${matchingShortcut.description}`, error);
      }
    }
  }, [matchesShortcut, handleAsync, addError, onShortcutTriggered]);

  // Set up keyboard event listeners
  useEffect(() => {
    if (!enabled) return;

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [enabled, handleKeyDown]);

  // Get shortcuts by category
  const getShortcutsByCategory = useCallback((category: ClaudeFlowKeyboardShortcut['category']) => {
    return shortcutsRef.current.filter(shortcut => shortcut.category === category);
  }, []);

  // Get all shortcuts
  const getAllShortcuts = useCallback(() => {
    return shortcutsRef.current;
  }, []);

  // Format shortcut key combination for display
  const formatShortcut = useCallback((shortcut: ClaudeFlowKeyboardShortcut): string => {
    const parts: string[] = [];
    
    if (shortcut.ctrlKey) parts.push('Ctrl');
    if (shortcut.altKey) parts.push('Alt');
    if (shortcut.shiftKey) parts.push('Shift');
    if (shortcut.metaKey) parts.push('Cmd');
    
    // Format special keys
    let key = shortcut.key;
    switch (key) {
      case 'ArrowLeft': key = '←'; break;
      case 'ArrowRight': key = '→'; break;
      case 'ArrowUp': key = '↑'; break;
      case 'ArrowDown': key = '↓'; break;
      case ' ': key = 'Space'; break;
      default: key = key.toUpperCase(); break;
    }
    
    parts.push(key);
    return parts.join(' + ');
  }, []);

  // Disable specific shortcut
  const disableShortcut = useCallback((key: string, modifiers?: Partial<Pick<ClaudeFlowKeyboardShortcut, 'ctrlKey' | 'altKey' | 'shiftKey' | 'metaKey'>>) => {
    const shortcut = shortcutsRef.current.find(s => {
      const matches = s.key === key;
      if (!matches) return false;
      
      if (modifiers) {
        return (
          (modifiers.ctrlKey ?? false) === (s.ctrlKey ?? false) &&
          (modifiers.altKey ?? false) === (s.altKey ?? false) &&
          (modifiers.shiftKey ?? false) === (s.shiftKey ?? false) &&
          (modifiers.metaKey ?? false) === (s.metaKey ?? false)
        );
      }
      
      return true;
    });
    
    if (shortcut) {
      shortcut.disabled = true;
    }
  }, []);

  // Enable specific shortcut
  const enableShortcut = useCallback((key: string, modifiers?: Partial<Pick<ClaudeFlowKeyboardShortcut, 'ctrlKey' | 'altKey' | 'shiftKey' | 'metaKey'>>) => {
    const shortcut = shortcutsRef.current.find(s => {
      const matches = s.key === key;
      if (!matches) return false;
      
      if (modifiers) {
        return (
          (modifiers.ctrlKey ?? false) === (s.ctrlKey ?? false) &&
          (modifiers.altKey ?? false) === (s.altKey ?? false) &&
          (modifiers.shiftKey ?? false) === (s.shiftKey ?? false) &&
          (modifiers.metaKey ?? false) === (s.metaKey ?? false)
        );
      }
      
      return true;
    });
    
    if (shortcut) {
      shortcut.disabled = false;
    }
  }, []);

  return {
    shortcuts: shortcutsRef.current,
    getShortcutsByCategory,
    getAllShortcuts,
    formatShortcut,
    disableShortcut,
    enableShortcut,
    enabled: isEnabledRef.current
  };
};

/**
 * Default keyboard shortcut categories for easy reference
 */
export const CLAUDE_FLOW_SHORTCUT_CATEGORIES = {
  NAVIGATION: 'navigation' as const,
  AGENTS: 'agents' as const,
  TASKS: 'tasks' as const,
  MONITORING: 'monitoring' as const,
  GENERAL: 'general' as const
};

/**
 * Utility function to check if the current platform uses Cmd key instead of Ctrl
 */
export const usesCmdKey = (): boolean => {
  return typeof navigator !== 'undefined' && /Mac|iPod|iPhone|iPad/.test(navigator.platform);
};