/**
 * Claude Flow WebSocket Integration
 * Handles real-time updates from <PERSON> through WebSocket connection
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Claude<PERSON>lowSystemStatus } from './claudeFlowIntegration';

export type ClaudeFlowEventType = 
  | 'agent.spawned'
  | 'agent.updated'
  | 'agent.terminated'
  | 'task.created'
  | 'task.assigned'
  | 'task.updated'
  | 'task.completed'
  | 'task.failed'
  | 'task.cancelled'
  | 'memory.stored'
  | 'memory.deleted'
  | 'system.status'
  | 'system.error'
  | 'workflow.started'
  | 'workflow.phase.completed'
  | 'workflow.completed'
  | 'workflow.failed';

export interface ClaudeFlowEvent {
  type: ClaudeFlowEventType;
  timestamp: string;
  data: any;
}

export interface ClaudeFlowWebSocketConfig {
  host: string;
  port: number;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
}

export class ClaudeFlowWebSocket {
  private config: ClaudeFlowWebSocketConfig;
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private eventHandlers: Map<ClaudeFlowEventType, Set<(event: ClaudeFlowEvent) => void>> = new Map();
  private connectionHandlers: {
    onConnect: Set<() => void>;
    onDisconnect: Set<(reason: string) => void>;
    onError: Set<(error: Error) => void>;
  } = {
    onConnect: new Set(),
    onDisconnect: new Set(),
    onError: new Set()
  };
  private isConnecting = false;
  private isConnected = false;

  constructor(config: ClaudeFlowWebSocketConfig) {
    this.config = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      ...config
    };
  }

  /**
   * Connect to Claude Flow WebSocket server
   */
  async connect(): Promise<void> {
    if (this.isConnected || this.isConnecting) {
      console.log('Claude Flow WebSocket: Already connected or connecting');
      return;
    }

    this.isConnecting = true;
    const wsUrl = `ws://${this.config.host}:${this.config.port}/ws`;

    try {
      console.log(`Claude Flow WebSocket: Connecting to ${wsUrl}...`);
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('Claude Flow WebSocket: Connected');
        this.isConnected = true;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        
        // Start heartbeat
        this.startHeartbeat();
        
        // Notify connection handlers
        this.connectionHandlers.onConnect.forEach(handler => handler());
      };

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('Claude Flow WebSocket: Failed to parse message', error);
        }
      };

      this.ws.onerror = (error) => {
        console.error('Claude Flow WebSocket: Error', error);
        this.connectionHandlers.onError.forEach(handler => 
          handler(new Error('WebSocket error'))
        );
      };

      this.ws.onclose = (event) => {
        console.log('Claude Flow WebSocket: Disconnected', event.code, event.reason);
        this.isConnected = false;
        this.isConnecting = false;
        
        // Stop heartbeat
        this.stopHeartbeat();
        
        // Notify disconnection handlers
        this.connectionHandlers.onDisconnect.forEach(handler => 
          handler(event.reason || 'Connection closed')
        );
        
        // Attempt to reconnect
        this.scheduleReconnect();
      };
    } catch (error) {
      console.error('Claude Flow WebSocket: Failed to connect', error);
      this.isConnecting = false;
      this.scheduleReconnect();
      throw error;
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.isConnected = false;
    this.isConnecting = false;
  }

  /**
   * Subscribe to specific event types
   */
  on(eventType: ClaudeFlowEventType, handler: (event: ClaudeFlowEvent) => void): () => void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());
    }
    
    this.eventHandlers.get(eventType)!.add(handler);
    
    // Return unsubscribe function
    return () => {
      this.eventHandlers.get(eventType)?.delete(handler);
    };
  }

  /**
   * Subscribe to connection events
   */
  onConnect(handler: () => void): () => void {
    this.connectionHandlers.onConnect.add(handler);
    return () => this.connectionHandlers.onConnect.delete(handler);
  }

  onDisconnect(handler: (reason: string) => void): () => void {
    this.connectionHandlers.onDisconnect.add(handler);
    return () => this.connectionHandlers.onDisconnect.delete(handler);
  }

  onError(handler: (error: Error) => void): () => void {
    this.connectionHandlers.onError.add(handler);
    return () => this.connectionHandlers.onError.delete(handler);
  }

  /**
   * Send a message to the server
   */
  send(message: any): void {
    if (!this.isConnected || !this.ws) {
      throw new Error('WebSocket is not connected');
    }
    
    this.ws.send(JSON.stringify(message));
  }

  /**
   * Check if connected
   */
  get connected(): boolean {
    return this.isConnected;
  }

  /**
   * Handle incoming messages
   */
  private handleMessage(message: any): void {
    // Handle different message types
    if (message.type === 'ping') {
      // Respond to ping
      this.send({ type: 'pong', timestamp: Date.now() });
      return;
    }

    if (message.type === 'event') {
      const event: ClaudeFlowEvent = {
        type: message.eventType,
        timestamp: message.timestamp || new Date().toISOString(),
        data: message.data
      };

      // Notify event handlers
      const handlers = this.eventHandlers.get(event.type);
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(event);
          } catch (error) {
            console.error(`Claude Flow WebSocket: Error in event handler for ${event.type}`, error);
          }
        });
      }
    }
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.stopHeartbeat();
    
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.ws) {
        this.send({ type: 'ping', timestamp: Date.now() });
      }
    }, this.config.heartbeatInterval!);
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts!) {
      console.error('Claude Flow WebSocket: Max reconnection attempts reached');
      return;
    }

    const delay = this.config.reconnectInterval! * Math.pow(2, this.reconnectAttempts);
    this.reconnectAttempts++;

    console.log(`Claude Flow WebSocket: Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);

    this.reconnectTimeout = setTimeout(() => {
      this.connect().catch(error => {
        console.error('Claude Flow WebSocket: Reconnection failed', error);
      });
    }, delay);
  }
}

/**
 * Create a WebSocket connection to Claude Flow
 */
export function createClaudeFlowWebSocket(config: Partial<ClaudeFlowWebSocketConfig> = {}): ClaudeFlowWebSocket {
  const defaultConfig: ClaudeFlowWebSocketConfig = {
    host: 'localhost',
    port: 8765,
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000
  };

  return new ClaudeFlowWebSocket({
    ...defaultConfig,
    ...config
  });
}