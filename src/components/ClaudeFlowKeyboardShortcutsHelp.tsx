import React from 'react';
import { motion } from 'framer-motion';
import { 
  Keyboard, 
  Navigation, 
  Bot, 
  Zap, 
  Activity, 
  Settings,
  X,
  ArrowLeft,
  ArrowRight,
  Search,
  RefreshCw,
  Plus,
  Monitor
} from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Claude<PERSON>lowKeyboardShortcut, 
  CLAUDE_FLOW_SHORTCUT_CATEGORIES,
  usesCmdKey 
} from '@/hooks/useClaudeFlowKeyboardShortcuts';

interface ClaudeFlowKeyboardShortcutsHelpProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  shortcuts: ClaudeFlowKeyboardShortcut[];
  formatShortcut: (shortcut: <PERSON><PERSON><PERSON>KeyboardShortcut) => string;
}

/**
 * Help dialog component for displaying Claude Flow keyboard shortcuts
 * Organized by category with visual icons and descriptions
 */
export const ClaudeFlowKeyboardShortcutsHelp: React.FC<ClaudeFlowKeyboardShortcutsHelpProps> = ({
  open,
  onOpenChange,
  shortcuts,
  formatShortcut
}) => {
  const cmdKey = usesCmdKey();

  // Group shortcuts by category
  const shortcutsByCategory = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, ClaudeFlowKeyboardShortcut[]>);

  // Category metadata
  const categoryInfo = {
    [CLAUDE_FLOW_SHORTCUT_CATEGORIES.NAVIGATION]: {
      title: 'Navigation',
      icon: <Navigation className="h-4 w-4" />,
      description: 'Move between different sections',
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/20'
    },
    [CLAUDE_FLOW_SHORTCUT_CATEGORIES.AGENTS]: {
      title: 'Agents',
      icon: <Bot className="h-4 w-4" />,
      description: 'Manage AI agents',
      color: 'text-purple-500',
      bgColor: 'bg-purple-500/10',
      borderColor: 'border-purple-500/20'
    },
    [CLAUDE_FLOW_SHORTCUT_CATEGORIES.TASKS]: {
      title: 'Tasks',
      icon: <Zap className="h-4 w-4" />,
      description: 'Create and manage tasks',
      color: 'text-green-500',
      bgColor: 'bg-green-500/10',
      borderColor: 'border-green-500/20'
    },
    [CLAUDE_FLOW_SHORTCUT_CATEGORIES.MONITORING]: {
      title: 'Monitoring',
      icon: <Activity className="h-4 w-4" />,
      description: 'System monitoring and metrics',
      color: 'text-orange-500',
      bgColor: 'bg-orange-500/10',
      borderColor: 'border-orange-500/20'
    },
    [CLAUDE_FLOW_SHORTCUT_CATEGORIES.GENERAL]: {
      title: 'General',
      icon: <Settings className="h-4 w-4" />,
      description: 'General application shortcuts',
      color: 'text-gray-500',
      bgColor: 'bg-gray-500/10',
      borderColor: 'border-gray-500/20'
    }
  };

  // Format shortcut with platform-specific modifier keys
  const formatShortcutForPlatform = (shortcut: ClaudeFlowKeyboardShortcut): string => {
    let formatted = formatShortcut(shortcut);
    if (cmdKey) {
      formatted = formatted.replace(/Ctrl/g, 'Cmd');
    }
    return formatted;
  };

  // Get shortcut icon based on the action
  const getShortcutIcon = (shortcut: ClaudeFlowKeyboardShortcut) => {
    if (shortcut.description.includes('refresh') || shortcut.description.includes('Refresh')) {
      return <RefreshCw className="h-3 w-3" />;
    }
    if (shortcut.description.includes('new') || shortcut.description.includes('create') || shortcut.description.includes('Spawn')) {
      return <Plus className="h-3 w-3" />;
    }
    if (shortcut.description.includes('search') || shortcut.description.includes('Search')) {
      return <Search className="h-3 w-3" />;
    }
    if (shortcut.description.includes('tab') || shortcut.description.includes('Switch')) {
      return <Monitor className="h-3 w-3" />;
    }
    if (shortcut.key === 'ArrowLeft') {
      return <ArrowLeft className="h-3 w-3" />;
    }
    if (shortcut.key === 'ArrowRight') {
      return <ArrowRight className="h-3 w-3" />;
    }
    return null;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Keyboard className="h-5 w-5 text-blue-500" />
            Keyboard Shortcuts
          </DialogTitle>
          <DialogDescription>
            Speed up your workflow with these keyboard shortcuts. Press <Badge variant="outline" className="mx-1">Esc</Badge> to close this dialog.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 overflow-y-auto pr-2 max-h-[60vh]">
          {Object.entries(shortcutsByCategory).map(([category, categoryShortcuts]) => {
            const info = categoryInfo[category as keyof typeof categoryInfo];
            if (!info || categoryShortcuts.length === 0) return null;

            return (
              <motion.div
                key={category}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Card className={`${info.borderColor} ${info.bgColor}`}>
                  <CardHeader className="pb-3">
                    <CardTitle className={`flex items-center gap-2 text-sm font-medium ${info.color}`}>
                      {info.icon}
                      {info.title}
                    </CardTitle>
                    <p className="text-xs text-muted-foreground">{info.description}</p>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {categoryShortcuts.map((shortcut, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.2, delay: index * 0.05 }}
                        className="flex items-center justify-between py-2 px-3 rounded-lg bg-background/50 hover:bg-background/80 transition-colors"
                      >
                        <div className="flex items-center gap-2 flex-1">
                          {getShortcutIcon(shortcut)}
                          <span className="text-sm text-foreground">{shortcut.description}</span>
                        </div>
                        <Badge 
                          variant="secondary" 
                          className="font-mono text-xs bg-muted/50 hover:bg-muted transition-colors"
                        >
                          {formatShortcutForPlatform(shortcut)}
                        </Badge>
                      </motion.div>
                    ))}
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        <Separator />

        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Keyboard className="h-3 w-3" />
            <span>
              {shortcuts.filter(s => !s.disabled).length} shortcuts available
              {cmdKey && ' • Using Cmd key on macOS'}
            </span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onOpenChange(false)}
            className="gap-2"
          >
            <X className="h-3 w-3" />
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

/**
 * Quick shortcut hint component for inline display
 */
export const ClaudeFlowShortcutHint: React.FC<{
  shortcut: string;
  className?: string;
}> = ({ shortcut, className = '' }) => {
  const cmdKey = usesCmdKey();
  const formatted = cmdKey ? shortcut.replace(/Ctrl/g, 'Cmd') : shortcut;

  return (
    <Badge 
      variant="outline" 
      className={`font-mono text-xs bg-muted/30 hover:bg-muted/50 transition-colors ${className}`}
    >
      {formatted}
    </Badge>
  );
};

/**
 * Floating shortcut indicator for actions
 */
export const ClaudeFlowActionWithShortcut: React.FC<{
  children: React.ReactNode;
  shortcut?: string;
  description?: string;
  className?: string;
}> = ({ children, shortcut, description, className = '' }) => {
  if (!shortcut) return <>{children}</>;

  return (
    <div className={`group relative ${className}`}>
      {children}
      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50">
        <div className="bg-popover border border-border rounded-md px-2 py-1 text-xs shadow-md whitespace-nowrap">
          <div className="flex items-center gap-2">
            {description && <span className="text-muted-foreground">{description}</span>}
            <ClaudeFlowShortcutHint shortcut={shortcut} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClaudeFlowKeyboardShortcutsHelp;