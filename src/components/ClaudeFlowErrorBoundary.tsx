import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertCircle, RefreshCw, Home, FileWarning, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { motion, AnimatePresence } from 'framer-motion';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
  errorCount: number;
}

export class ClaudeFlowErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      errorCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Claude Flow Error Boundary caught an error:', error, errorInfo);
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Update state with error details
    this.setState(prevState => ({
      errorInfo,
      errorCount: prevState.errorCount + 1
    }));

    // Log to error tracking service (if configured)
    this.logErrorToService(error, errorInfo);
  }

  logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // TODO: Integrate with error tracking service (e.g., Sentry, LogRocket)
    // For now, just log to console with structured format
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      context: 'ClaudeFlow',
      errorCount: this.state.errorCount + 1
    };
    
    console.error('Claude Flow Error Report:', errorData);
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false
    });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails
    }));
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return <>{this.props.fallback}</>;
      }

      const { error, errorInfo, showDetails, errorCount } = this.state;

      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="min-h-screen flex items-center justify-center p-4 bg-background"
        >
          <Card className="w-full max-w-2xl p-8 shadow-lg border-destructive/20">
            <div className="flex flex-col items-center text-center space-y-6">
              {/* Error Icon */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 200, damping: 15 }}
                className="p-4 bg-destructive/10 rounded-full"
              >
                <AlertCircle className="h-12 w-12 text-destructive" />
              </motion.div>

              {/* Error Title */}
              <div className="space-y-2">
                <h1 className="text-2xl font-bold text-foreground">
                  Claude Flow Error
                </h1>
                <p className="text-muted-foreground">
                  Something went wrong in the Claude Flow system
                </p>
                {errorCount > 1 && (
                  <p className="text-sm text-destructive">
                    This error has occurred {errorCount} times
                  </p>
                )}
              </div>

              {/* Error Message */}
              {error && (
                <Card className="w-full p-4 bg-muted/30 border-muted">
                  <div className="flex items-start gap-3">
                    <FileWarning className="h-5 w-5 text-destructive mt-0.5 flex-shrink-0" />
                    <div className="flex-1 text-left">
                      <p className="font-mono text-sm text-foreground break-all">
                        {error.message}
                      </p>
                    </div>
                  </div>
                </Card>
              )}

              {/* Actions */}
              <div className="flex flex-wrap gap-3 justify-center">
                <Button
                  onClick={this.handleReset}
                  variant="default"
                  className="gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </Button>
                <Button
                  onClick={this.handleGoHome}
                  variant="outline"
                  className="gap-2"
                >
                  <Home className="h-4 w-4" />
                  Go Home
                </Button>
                <Button
                  onClick={this.toggleDetails}
                  variant="ghost"
                  className="gap-2"
                >
                  {showDetails ? (
                    <>
                      <ChevronUp className="h-4 w-4" />
                      Hide Details
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-4 w-4" />
                      Show Details
                    </>
                  )}
                </Button>
              </div>

              {/* Error Details */}
              <AnimatePresence>
                {showDetails && errorInfo && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="w-full"
                  >
                    <Card className="p-4 bg-muted/20 border-muted">
                      <div className="space-y-4 text-left">
                        {/* Stack Trace */}
                        {error?.stack && (
                          <div className="space-y-2">
                            <h3 className="text-sm font-semibold text-muted-foreground">
                              Stack Trace:
                            </h3>
                            <pre className="text-xs font-mono overflow-x-auto whitespace-pre-wrap break-all p-3 bg-background rounded-md">
                              {error.stack}
                            </pre>
                          </div>
                        )}

                        {/* Component Stack */}
                        {errorInfo.componentStack && (
                          <div className="space-y-2">
                            <h3 className="text-sm font-semibold text-muted-foreground">
                              Component Stack:
                            </h3>
                            <pre className="text-xs font-mono overflow-x-auto whitespace-pre-wrap break-all p-3 bg-background rounded-md">
                              {errorInfo.componentStack}
                            </pre>
                          </div>
                        )}
                      </div>
                    </Card>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Help Text */}
              <div className="text-sm text-muted-foreground space-y-1">
                <p>If this error persists, please try:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Refreshing the page</li>
                  <li>Checking your Claude Flow server connection</li>
                  <li>Clearing your browser cache</li>
                  <li>Reporting the issue to support</li>
                </ul>
              </div>
            </div>
          </Card>
        </motion.div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook to wrap functional components with error boundary
 */
export function withClaudeFlowErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: ErrorInfo) => void
) {
  return (props: P) => (
    <ClaudeFlowErrorBoundary fallback={fallback} onError={onError}>
      <Component {...props} />
    </ClaudeFlowErrorBoundary>
  );
}

/**
 * Error recovery strategies for specific Claude Flow errors
 */
export const claudeFlowErrorRecovery = {
  connectionError: () => {
    // Attempt to reconnect to Claude Flow server
    console.log('Attempting to recover from connection error...');
    // TODO: Implement reconnection logic
  },
  
  agentError: (agentId: string) => {
    // Attempt to restart or recover specific agent
    console.log(`Attempting to recover agent ${agentId}...`);
    // TODO: Implement agent recovery logic
  },
  
  taskError: (taskId: string) => {
    // Attempt to retry or recover specific task
    console.log(`Attempting to recover task ${taskId}...`);
    // TODO: Implement task recovery logic
  },
  
  memoryError: () => {
    // Clear memory cache and retry
    console.log('Attempting to recover from memory error...');
    // TODO: Implement memory recovery logic
  }
};