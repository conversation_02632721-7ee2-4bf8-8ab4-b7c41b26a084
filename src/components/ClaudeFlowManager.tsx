import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Claude<PERSON>lowErrorBoundary } from "./ClaudeFlowErrorBoundary";
import { useClaudeFlowError } from "@/hooks/useClaudeFlowError";
import { useClaude<PERSON>lowKeyboardShortcuts, ClaudeFlowKeyboardActions } from "@/hooks/useClaudeFlowKeyboardShortcuts";
import { ClaudeFlowKeyboardShortcutsHelp, ClaudeFlowActionWithShortcut } from "./ClaudeFlowKeyboardShortcutsHelp";
import { 
  ArrowLeft, 
  Bot, 
  Zap, 
  Play, 
  Pause, 
  Square, 
  Plus, 
  Trash2, 
  Activity, 
  Brain, 
  Settings, 
  Monitor,
  Terminal,
  Network,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  RefreshCw,
  Search,
  Code,
  FileText,
  Shield,
  GitBranch
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Tabs, TabsList, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Slider } from "@/components/ui/slider";
import { Toast, ToastContainer } from "@/components/ui/toast";
import { ClaudeFlowTerminal } from "./ClaudeFlowTerminal";
import { ClaudeFlowMonitoring } from "./ClaudeFlowMonitoring";
import { ClaudeFlowMemory } from "./ClaudeFlowMemory";
import { ClaudeFlowWorkflow } from "./ClaudeFlowWorkflow";
import { 
  ClaudeFlowIntegration, 
  ClaudeFlowAgent, 
  ClaudeFlowTask, 
  ClaudeFlowSystemStatus,
  DEFAULT_CLAUDE_FLOW_CONFIG,
  CLAUDE_FLOW_MCP_CONFIG 
} from "@/lib/claudeFlowIntegration";
import { createMCPClient } from "@/lib/mcpClient";
import { useMCP } from "@/contexts/MCPContext";
import { 
  useClaudeFlowWebSocket, 
  useClaudeFlowAgentUpdates, 
  useClaudeFlowTaskUpdates,
  useClaudeFlowSystemStatus 
} from "@/hooks/useClaudeFlowWebSocket";

interface ClaudeFlowManagerProps {
  onBack: () => void;
  className?: string;
}

/**
 * Claude-Flow Manager Component
 * Provides a comprehensive UI for managing Claude-Flow agent orchestration
 */
const ClaudeFlowManagerInner: React.FC<ClaudeFlowManagerProps> = ({
  onBack,
  className
}) => {
  // MCP Context
  const { servers, serverStatuses, addServerToState, updateServerStatus, loadServers } = useMCP();
  
  // Error handling
  const { addError, handleAsync, clearErrors } = useClaudeFlowError({
    onError: (error) => {
      showToast(error.message, 'error');
    },
    onRecovery: () => {
      showToast('Successfully recovered from error', 'success');
    }
  });
  
  // State management
  const [integration] = useState(() => new ClaudeFlowIntegration(DEFAULT_CLAUDE_FLOW_CONFIG));
  const [agents, setAgents] = useState<ClaudeFlowAgent[]>([]);
  const [tasks, setTasks] = useState<ClaudeFlowTask[]>([]);
  const [systemStatus, setSystemStatus] = useState<ClaudeFlowSystemStatus | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [toast, setToast] = useState<{ message: string; type: "success" | "error" } | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [showSpawnAgentDialog, setShowSpawnAgentDialog] = useState(false);
  const [showCreateTaskDialog, setShowCreateTaskDialog] = useState(false);
  const [spawnAgentLoading, setSpawnAgentLoading] = useState(false);
  const [createTaskLoading, setCreateTaskLoading] = useState(false);
  
  // Refs
  const toastTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // WebSocket integration
  const { state: wsState, actions: wsActions } = useClaudeFlowWebSocket({
    host: DEFAULT_CLAUDE_FLOW_CONFIG.host,
    port: DEFAULT_CLAUDE_FLOW_CONFIG.port,
    autoConnect: false, // We'll connect after initialization
    onConnect: () => {
      console.log('Claude Flow WebSocket connected');
      showToast('Real-time updates connected', 'success');
    },
    onDisconnect: (reason) => {
      console.log('Claude Flow WebSocket disconnected:', reason);
      showToast('Real-time updates disconnected', 'error');
    },
    onError: (error) => {
      console.error('Claude Flow WebSocket error:', error);
    }
  });

  // Real-time agent updates
  useClaudeFlowAgentUpdates((agent, eventType) => {
    console.log('Agent update:', eventType, agent);
    
    switch (eventType) {
      case 'agent.spawned':
        setAgents(prev => [...prev, agent]);
        break;
      case 'agent.updated':
        setAgents(prev => prev.map(a => a.id === agent.id ? agent : a));
        break;
      case 'agent.terminated':
        setAgents(prev => prev.map(a => a.id === agent.id ? { ...a, status: 'terminated' } : a));
        break;
    }
  });

  // Real-time task updates
  useClaudeFlowTaskUpdates((task, eventType) => {
    console.log('Task update:', eventType, task);
    
    switch (eventType) {
      case 'task.created':
        setTasks(prev => [...prev, task]);
        break;
      case 'task.assigned':
      case 'task.updated':
      case 'task.completed':
      case 'task.failed':
      case 'task.cancelled':
        setTasks(prev => prev.map(t => t.id === task.id ? task : t));
        break;
    }
  });

  // Real-time system status updates
  useClaudeFlowSystemStatus((status) => {
    console.log('System status update:', status);
    setSystemStatus(status);
  });

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (toastTimeoutRef.current) {
        clearTimeout(toastTimeoutRef.current);
      }
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      wsActions.disconnect();
    };
  }, [wsActions]);

  // Initialize integration and load data
  useEffect(() => {
    const initializeIntegration = async () => {
      try {
        setLoading(true);
        setError(null);
        clearErrors();
        
        // Check if Claude Flow server is configured in MCP
        const claudeFlowServer = servers.find(s => s.name === 'claude-flow');
        
        if (!claudeFlowServer) {
          // Add Claude Flow server to MCP if not present
          console.log('Claude Flow server not found in MCP, needs to be added');
          const error = 'Claude Flow server not configured. The configuration has been added to .mcp.json. Please restart Claudia to load the new MCP server.';
          setError(error);
          addError('connection', error, { requiresRestart: true }, false);
          setLoading(false);
          return;
        }
        
        // Create MCP client for Claude Flow
        const mcpClient = createMCPClient('claude-flow');
        
        // Initialize the integration with error handling
        await handleAsync(
          () => integration.initialize(mcpClient),
          'connection',
          'Failed to initialize Claude Flow integration'
        );
        setIsInitialized(true);
        
        // Load initial data
        await loadData();
        
        // Connect WebSocket for real-time updates
        try {
          await wsActions.connect();
        } catch (wsError) {
          console.error('Failed to connect WebSocket:', wsError);
          addError('connection', 'WebSocket connection failed', wsError, true);
          // Continue without WebSocket - we'll still have periodic refresh
        }
        
        // Set up periodic refresh as fallback
        refreshIntervalRef.current = setInterval(loadData, 30000); // Refresh every 30 seconds as fallback
        
      } catch (err) {
        console.error('Failed to initialize Claude-Flow integration:', err);
        const errorMessage = 'Failed to connect to Claude-Flow. Please ensure Claude-Flow is running and accessible.';
        setError(errorMessage);
        addError('connection', errorMessage, err, true);
      } finally {
        setLoading(false);
      }
    };

    initializeIntegration();
  }, [servers, integration, handleAsync, addError, clearErrors]);
  
  // Re-load data when initialization status changes
  useEffect(() => {
    if (isInitialized) {
      loadData();
    }
  }, [isInitialized]);

  /**
   * Load all data from Claude-Flow
   */
  const loadData = async () => {
    try {
      if (isInitialized) {
        // Load real data from Claude Flow with error handling
        const [agentList, taskList, status] = await Promise.all([
          handleAsync(() => integration.listAgents(), 'general', 'Failed to load agents'),
          handleAsync(() => integration.listTasks({ limit: 50 }), 'general', 'Failed to load tasks'),
          handleAsync(() => integration.getSystemStatus(), 'general', 'Failed to load system status')
        ]);
        
        if (agentList) setAgents(agentList);
        if (taskList) setTasks(taskList);
        if (status) setSystemStatus(status);
      } else {
        // Use mock data if not initialized
        const mockAgents: ClaudeFlowAgent[] = [
        {
          id: 'agent_1',
          name: 'Research Assistant',
          type: 'researcher',
          status: 'running',
          capabilities: ['web-search', 'analysis', 'reporting'],
          systemPrompt: 'You are a research assistant specialized in gathering and analyzing information.',
          maxConcurrentTasks: 3,
          priority: 8,
          createdAt: new Date().toISOString(),
          lastActivity: new Date().toISOString()
        },
        {
          id: 'agent_2',
          name: 'Code Developer',
          type: 'implementer',
          status: 'idle',
          capabilities: ['coding', 'debugging', 'testing'],
          systemPrompt: 'You are a software developer focused on writing clean, efficient code.',
          maxConcurrentTasks: 2,
          priority: 7,
          createdAt: new Date().toISOString()
        }
      ];

      const mockTasks: ClaudeFlowTask[] = [
        {
          id: 'task_1',
          type: 'research',
          description: 'Analyze latest AI development trends',
          status: 'running',
          priority: 8,
          agentId: 'agent_1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'task_2',
          type: 'implementation',
          description: 'Implement user authentication system',
          status: 'pending',
          priority: 7,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      const mockStatus: ClaudeFlowSystemStatus = {
        orchestrator: {
          running: true,
          uptime: 3600,
          totalAgents: 2,
          activeAgents: 1,
          totalTasks: 2,
          completedTasks: 5
        },
        memory: {
          totalEntries: 150,
          memoryUsage: 0.25
        },
        performance: {
          avgResponseTime: 1200,
          throughput: 15.5,
          errorRate: 0.02
        }
      };

        setAgents(mockAgents);
        setTasks(mockTasks);
        setSystemStatus(mockStatus);
      }
      
    } catch (err) {
      console.error('Failed to load Claude-Flow data:', err);
      showToast('Failed to load data from Claude-Flow', 'error');
    }
  };

  /**
   * Show toast notification
   */
  const showToast = (message: string, type: "success" | "error") => {
    if (toastTimeoutRef.current) {
      clearTimeout(toastTimeoutRef.current);
    }
    
    setToast({ message, type });
    
    toastTimeoutRef.current = setTimeout(() => {
      setToast(null);
      toastTimeoutRef.current = null;
    }, 3000);
  };

  /**
   * Format uptime in human readable format
   */
  const formatUptime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  /**
   * Get status badge variant
   */
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'running': return 'success';
      case 'idle': return 'secondary';
      case 'paused': return 'warning';
      case 'error': return 'destructive';
      case 'terminated': return 'secondary';
      default: return 'secondary';
    }
  };

  /**
   * Get status icon
   */
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="h-3 w-3" />;
      case 'idle': return <Pause className="h-3 w-3" />;
      case 'error': return <XCircle className="h-3 w-3" />;
      case 'terminated': return <Square className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  /**
   * Get agent type icon
   */
  const getAgentTypeIcon = (type: string) => {
    switch (type) {
      case 'researcher': return <Search className="h-4 w-4" />;
      case 'implementer': return <Code className="h-4 w-4" />;
      case 'reviewer': return <FileText className="h-4 w-4" />;
      case 'security': return <Shield className="h-4 w-4" />;
      default: return <Bot className="h-4 w-4" />;
    }
  };

  /**
   * Spawn a new agent
   */
  const handleSpawnAgent = async (agentConfig: any) => {
    if (!isInitialized) {
      showToast('Claude Flow not initialized', 'error');
      return;
    }

    setSpawnAgentLoading(true);
    try {
      const newAgent = await handleAsync(
        () => integration.spawnAgent(agentConfig),
        'agent',
        'Failed to spawn agent'
      );
      
      if (newAgent) {
        showToast(`Agent "${newAgent.name}" spawned successfully!`, 'success');
        setShowSpawnAgentDialog(false);
        // Reload agents list
        await loadData();
      }
    } catch (err) {
      // Error already handled by handleAsync
      console.error('Failed to spawn agent:', err);
    } finally {
      setSpawnAgentLoading(false);
    }
  };

  /**
   * Create a new task
   */
  const handleCreateTask = async (taskConfig: any) => {
    if (!isInitialized) {
      showToast('Claude Flow not initialized', 'error');
      return;
    }

    setCreateTaskLoading(true);
    try {
      const newTask = await handleAsync(
        () => integration.createTask(taskConfig),
        'task',
        'Failed to create task'
      );
      
      if (newTask) {
        showToast(`Task created successfully!`, 'success');
        setShowCreateTaskDialog(false);
        // Reload tasks list
        await loadData();
      }
    } catch (err) {
      // Error already handled by handleAsync
      console.error('Failed to create task:', err);
    } finally {
      setCreateTaskLoading(false);
    }
  };

  /**
   * Terminate an agent
   */
  const handleTerminateAgent = async (agentId: string, agentName: string) => {
    if (!isInitialized) {
      showToast('Claude Flow not initialized', 'error');
      return;
    }

    try {
      await handleAsync(
        () => integration.terminateAgent(agentId, 'User requested termination'),
        'agent',
        `Failed to terminate agent "${agentName}"`
      );
      
      showToast(`Agent "${agentName}" terminated`, 'success');
      // Reload agents list
      await loadData();
    } catch (err) {
      // Error already handled by handleAsync
      console.error('Failed to terminate agent:', err);
    }
  };

  /**
   * Cancel a task
   */
  const handleCancelTask = async (taskId: string) => {
    if (!isInitialized) {
      showToast('Claude Flow not initialized', 'error');
      return;
    }

    try {
      await handleAsync(
        () => integration.cancelTask(taskId, 'User requested cancellation'),
        'task',
        'Failed to cancel task'
      );
      
      showToast('Task cancelled', 'success');
      // Reload tasks list
      await loadData();
    } catch (err) {
      // Error already handled by handleAsync
      console.error('Failed to cancel task:', err);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col h-screen max-h-screen bg-background text-foreground">
        <div className="flex-1 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col items-center gap-4 p-8 rounded-xl bg-card/50 backdrop-blur-sm border border-border/20"
          >
            <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500/20 to-blue-500/20 backdrop-blur-sm">
              <Loader2 className="h-8 w-8 animate-spin text-purple-500" />
            </div>
            <p className="text-sm text-muted-foreground font-medium">Connecting to Claude-Flow...</p>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-screen max-h-screen bg-background text-foreground ${className || ""}`}>
      <div className="max-w-7xl mx-auto w-full flex flex-col h-full">
        {/* Header */}
        <motion.header
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="flex-shrink-0 relative z-10"
        >
          <div className="absolute inset-0 bg-background/80 backdrop-blur-xl border-b border-white/5" />
          
          <div className="relative px-4 sm:px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 min-w-0 flex-1">
                <Button
                  variant="glass"
                  size="icon"
                  onClick={onBack}
                  className="h-10 w-10 flex-shrink-0 hover:scale-105 transition-transform"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  <div className="p-2.5 rounded-xl bg-gradient-to-br from-purple-500/20 to-blue-500/20 backdrop-blur-sm border border-purple-500/20 shadow-lg">
                    <Bot className="h-6 w-6 text-purple-500" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <h1 className="text-xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                      Claude-Flow Orchestration
                    </h1>
                    <div className="text-sm text-muted-foreground truncate">
                      Advanced AI agent management & coordination
                      {systemStatus && (
                        <span className="ml-2 inline-flex items-center gap-2">
                          <span className="inline-flex items-center gap-1">
                            <span className={`h-2 w-2 rounded-full animate-pulse inline-block ${
                              systemStatus.orchestrator.running ? 'bg-green-500' : 'bg-red-500'
                            }`} />
                            {systemStatus.orchestrator.running ? 'Online' : 'Offline'}
                          </span>
                          {wsState.connected && (
                            <span className="inline-flex items-center gap-1 text-xs">
                              <Network className="h-3 w-3 text-green-500" />
                              <span className="text-green-500">Live</span>
                            </span>
                          )}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* System Stats */}
              {systemStatus && (
                <div className="hidden lg:flex items-center gap-3">
                  <div className="px-3 py-1.5 rounded-lg bg-card/50 backdrop-blur-sm border border-border/20">
                    <div className="text-xs text-muted-foreground">Agents</div>
                    <div className="text-lg font-bold text-purple-500">
                      {systemStatus.orchestrator.activeAgents}/{systemStatus.orchestrator.totalAgents}
                    </div>
                  </div>
                  <div className="px-3 py-1.5 rounded-lg bg-blue-500/10 backdrop-blur-sm border border-blue-500/20">
                    <div className="text-xs text-blue-600 dark:text-blue-400">Tasks</div>
                    <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                      {systemStatus.orchestrator.totalTasks}
                    </div>
                  </div>
                  <div className="px-3 py-1.5 rounded-lg bg-green-500/10 backdrop-blur-sm border border-green-500/20">
                    <div className="text-xs text-green-600 dark:text-green-400">Uptime</div>
                    <div className="text-sm font-bold text-green-600 dark:text-green-400">
                      {formatUptime(systemStatus.orchestrator.uptime)}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </motion.header>

        {/* Error Display */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex-shrink-0 mx-4 sm:mx-6 mb-2 p-3 rounded-lg bg-destructive/10 border border-destructive/20 flex items-start gap-2 text-sm text-destructive"
            >
              <AlertCircle className="h-4 w-4 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <span>{error}</span>
                {error.includes('not configured') && (
                  <div className="mt-2 flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={async () => {
                        await loadServers();
                        // Retry initialization after reloading servers
                        window.location.reload();
                      }}
                      className="h-7 px-3 text-xs border-destructive/50 hover:bg-destructive/20"
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Reload MCP Servers
                    </Button>
                    <span className="text-xs text-muted-foreground">or</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Navigate to MCP settings to add Claude Flow
                        window.dispatchEvent(new CustomEvent('navigate-to-mcp-add', { 
                          detail: { 
                            serverConfig: CLAUDE_FLOW_MCP_CONFIG 
                          } 
                        }));
                      }}
                      className="h-7 px-3 text-xs border-destructive/50 hover:bg-destructive/20"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Configure Manually
                    </Button>
                  </div>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={loadData}
                className="h-6 px-2 text-xs hover:bg-destructive/20"
              >
                <RefreshCw className="h-3 w-3" />
              </Button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content */}
        <main className="flex-1 overflow-hidden grid grid-rows-[auto_1fr] gap-6 px-4 sm:px-6 pb-6">
          {/* Tab Navigation */}
          <nav className="sticky top-0 bg-background/95 backdrop-blur-sm z-10">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full max-w-4xl mx-auto grid-cols-7 h-12 p-1 bg-white/5 border border-white/10 backdrop-blur-xl">
                <TabsTrigger value="overview" className="gap-2 text-sm font-medium">
                  <Monitor className="h-4 w-4 text-purple-500" />
                  <span className="hidden sm:inline">Overview</span>
                </TabsTrigger>
                <TabsTrigger value="agents" className="gap-2 text-sm font-medium">
                  <Bot className="h-4 w-4 text-blue-500" />
                  <span className="hidden sm:inline">Agents</span>
                </TabsTrigger>
                <TabsTrigger value="tasks" className="gap-2 text-sm font-medium">
                  <Zap className="h-4 w-4 text-green-500" />
                  <span className="hidden sm:inline">Tasks</span>
                </TabsTrigger>
                <TabsTrigger value="workflows" className="gap-2 text-sm font-medium">
                  <GitBranch className="h-4 w-4 text-orange-500" />
                  <span className="hidden sm:inline">Workflow</span>
                </TabsTrigger>
                <TabsTrigger value="memory" className="gap-2 text-sm font-medium">
                  <Brain className="h-4 w-4 text-orange-500" />
                  <span className="hidden sm:inline">Memory</span>
                </TabsTrigger>
                <TabsTrigger value="monitoring" className="gap-2 text-sm font-medium">
                  <Activity className="h-4 w-4 text-cyan-500" />
                  <span className="hidden sm:inline">Monitor</span>
                </TabsTrigger>
                <TabsTrigger value="terminal" className="gap-2 text-sm font-medium">
                  <Terminal className="h-4 w-4 text-pink-500" />
                  <span className="hidden sm:inline">Terminal</span>
                </TabsTrigger>
              </TabsList>
              
              {/* Tab Content */}
              <div className="mt-6 overflow-hidden">
                {/* Overview Panel */}
                <TabsContent value="overview" className="m-0">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* System Status Card */}
                    <Card className="lg:col-span-2 border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-blue-500/5">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Activity className="h-5 w-5 text-purple-500" />
                          System Status
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        {systemStatus ? (
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Orchestrator</span>
                                <Badge variant={systemStatus.orchestrator.running ? "success" : "destructive"}>
                                  {systemStatus.orchestrator.running ? "Running" : "Stopped"}
                                </Badge>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Total Agents</span>
                                <span className="font-medium">{systemStatus.orchestrator.totalAgents}</span>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Active Agents</span>
                                <span className="font-medium text-green-500">{systemStatus.orchestrator.activeAgents}</span>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Total Tasks</span>
                                <span className="font-medium">{systemStatus.orchestrator.totalTasks}</span>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Completed</span>
                                <span className="font-medium text-blue-500">{systemStatus.orchestrator.completedTasks}</span>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Error Rate</span>
                                <span className="font-medium text-orange-500">{(systemStatus.performance.errorRate * 100).toFixed(1)}%</span>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center justify-center py-8">
                            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Performance Metrics */}
                    <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-green-500/5">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Zap className="h-5 w-5 text-blue-500" />
                          Performance
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        {systemStatus ? (
                          <div className="space-y-4">
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-muted-foreground">Avg Response</span>
                                <span className="text-sm font-medium">{systemStatus.performance.avgResponseTime}ms</span>
                              </div>
                              <div className="w-full bg-secondary rounded-full h-2">
                                <div 
                                  className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                                  style={{ width: `${Math.min(systemStatus.performance.avgResponseTime / 2000 * 100, 100)}%` }}
                                />
                              </div>
                            </div>
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-muted-foreground">Throughput</span>
                                <span className="text-sm font-medium">{systemStatus.performance.throughput.toFixed(1)}/sec</span>
                              </div>
                              <div className="w-full bg-secondary rounded-full h-2">
                                <div 
                                  className="bg-green-500 h-2 rounded-full transition-all duration-300" 
                                  style={{ width: `${Math.min(systemStatus.performance.throughput / 50 * 100, 100)}%` }}
                                />
                              </div>
                            </div>
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-muted-foreground">Memory Usage</span>
                                <span className="text-sm font-medium">{(systemStatus.memory.memoryUsage * 100).toFixed(1)}%</span>
                              </div>
                              <div className="w-full bg-secondary rounded-full h-2">
                                <div 
                                  className="bg-orange-500 h-2 rounded-full transition-all duration-300" 
                                  style={{ width: `${systemStatus.memory.memoryUsage * 100}%` }}
                                />
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center justify-center py-8">
                            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Agents Panel */}
                <TabsContent value="agents" className="m-0">
                  <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-purple-500/5">
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Bot className="h-5 w-5 text-blue-500" />
                        Active Agents ({agents.length})
                      </CardTitle>
                      <Button 
                        size="sm" 
                        className="gap-2"
                        onClick={() => setShowSpawnAgentDialog(true)}
                        disabled={!isInitialized}
                      >
                        <Plus className="h-4 w-4" />
                        Spawn Agent
                      </Button>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {agents.map((agent) => (
                          <div key={agent.id} className="p-4 rounded-lg bg-card/50 border border-border/20">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-3 mb-2">
                                  <h3 className="font-medium">{agent.name}</h3>
                                  <Badge variant={getStatusBadgeVariant(agent.status)} className="gap-1">
                                    {getStatusIcon(agent.status)}
                                    {agent.status}
                                  </Badge>
                                  <Badge variant="outline">{agent.type}</Badge>
                                </div>
                                <p className="text-sm text-muted-foreground mb-2">
                                  {agent.systemPrompt || 'No system prompt configured'}
                                </p>
                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                  <span>Priority: {agent.priority}</span>
                                  <span>Max Tasks: {agent.maxConcurrentTasks}</span>
                                  <span>Capabilities: {agent.capabilities.join(', ')}</span>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button variant="outline" size="sm">
                                  <Settings className="h-3 w-3" />
                                </Button>
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  className="text-destructive hover:text-destructive"
                                  onClick={() => handleTerminateAgent(agent.id, agent.name)}
                                  disabled={agent.status === 'terminated'}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                        
                        {agents.length === 0 && (
                          <div className="text-center py-12">
                            <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-medium mb-2">No Active Agents</h3>
                            <p className="text-muted-foreground mb-4">Spawn your first agent to begin orchestration</p>
                            <Button 
                              className="gap-2"
                              onClick={() => setShowSpawnAgentDialog(true)}
                              disabled={!isInitialized}
                            >
                              <Plus className="h-4 w-4" />
                              Spawn Agent
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Tasks Panel */}
                <TabsContent value="tasks" className="m-0">
                  <Card className="border-green-500/20 bg-gradient-to-br from-green-500/5 to-blue-500/5">
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5 text-green-500" />
                        Task Queue ({tasks.length})
                      </CardTitle>
                      <Button 
                        size="sm" 
                        className="gap-2"
                        onClick={() => setShowCreateTaskDialog(true)}
                        disabled={!isInitialized}
                      >
                        <Plus className="h-4 w-4" />
                        Create Task
                      </Button>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {tasks.map((task) => (
                          <div key={task.id} className="p-4 rounded-lg bg-card/50 border border-border/20">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-3 mb-2">
                                  <h3 className="font-medium">{task.description}</h3>
                                  <Badge variant={getStatusBadgeVariant(task.status)} className="gap-1">
                                    {getStatusIcon(task.status)}
                                    {task.status}
                                  </Badge>
                                  <Badge variant="outline">{task.type}</Badge>
                                </div>
                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                  <span>Priority: {task.priority}</span>
                                  {task.agentId && <span>Agent: {task.agentId}</span>}
                                  <span>Created: {new Date(task.createdAt).toLocaleDateString()}</span>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button variant="outline" size="sm">
                                  <Settings className="h-3 w-3" />
                                </Button>
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  className="text-destructive hover:text-destructive"
                                  onClick={() => handleCancelTask(task.id)}
                                  disabled={task.status === 'completed' || task.status === 'cancelled'}
                                >
                                  <XCircle className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                        
                        {tasks.length === 0 && (
                          <div className="text-center py-12">
                            <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-medium mb-2">No Active Tasks</h3>
                            <p className="text-muted-foreground mb-4">Create tasks to distribute work among agents</p>
                            <Button 
                              className="gap-2"
                              onClick={() => setShowCreateTaskDialog(true)}
                              disabled={!isInitialized}
                            >
                              <Plus className="h-4 w-4" />
                              Create Task
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Workflows Panel */}
                <TabsContent value="workflows" className="m-0">
                  <ClaudeFlowWorkflow integration={integration} />
                </TabsContent>

                {/* Memory Panel */}
                <TabsContent value="memory" className="m-0">
                  <ClaudeFlowMemory integration={integration} />
                </TabsContent>

                {/* Monitoring Panel */}
                <TabsContent value="monitoring" className="m-0">
                  <ClaudeFlowMonitoring integration={integration} />
                </TabsContent>

                {/* Terminal Panel */}
                <TabsContent value="terminal" className="m-0">
                  <ClaudeFlowTerminal integration={integration} />
                </TabsContent>
              </div>
            </Tabs>
          </nav>
        </main>
      </div>

      {/* Toast Notifications */}
      <ToastContainer>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onDismiss={() => {
              setToast(null);
              if (toastTimeoutRef.current) {
                clearTimeout(toastTimeoutRef.current);
                toastTimeoutRef.current = null;
              }
            }}
          />
        )}
      </ToastContainer>

      {/* Spawn Agent Dialog */}
      <SpawnAgentDialog
        open={showSpawnAgentDialog}
        onOpenChange={setShowSpawnAgentDialog}
        onSpawnAgent={handleSpawnAgent}
        loading={spawnAgentLoading}
      />

      {/* Create Task Dialog */}
      <CreateTaskDialog
        open={showCreateTaskDialog}
        onOpenChange={setShowCreateTaskDialog}
        onCreateTask={handleCreateTask}
        agents={agents}
        loading={createTaskLoading}
      />
    </div>
  );
};

/**
 * Dialog for spawning a new agent
 */
const SpawnAgentDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSpawnAgent: (config: any) => void;
  loading: boolean;
}> = ({ open, onOpenChange, onSpawnAgent, loading }) => {
  const [agentType, setAgentType] = useState('researcher');
  const [agentName, setAgentName] = useState('');
  const [systemPrompt, setSystemPrompt] = useState('');
  const [priority, setPriority] = useState(5);
  const [maxConcurrentTasks, setMaxConcurrentTasks] = useState(3);
  const [capabilities, setCapabilities] = useState<string[]>([]);

  const handleSubmit = () => {
    if (!agentName.trim()) {
      return;
    }

    onSpawnAgent({
      type: agentType,
      name: agentName,
      systemPrompt: systemPrompt || undefined,
      priority,
      maxConcurrentTasks,
      capabilities: capabilities.length > 0 ? capabilities : undefined
    });
  };

  const handleCapabilitiesChange = (value: string) => {
    const caps = value.split(',').map(c => c.trim()).filter(c => c);
    setCapabilities(caps);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-blue-500" />
            Spawn New Agent
          </DialogTitle>
          <DialogDescription>
            Create a new AI agent with specific capabilities and configuration
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Agent Type */}
          <div className="space-y-2">
            <Label htmlFor="agent-type">Agent Type</Label>
            <Select value={agentType} onValueChange={setAgentType}>
              <SelectTrigger id="agent-type">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="researcher">
                  <div className="flex items-center gap-2">
                    <Search className="h-4 w-4" />
                    Researcher
                  </div>
                </SelectItem>
                <SelectItem value="implementer">
                  <div className="flex items-center gap-2">
                    <Code className="h-4 w-4" />
                    Implementer
                  </div>
                </SelectItem>
                <SelectItem value="reviewer">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Reviewer
                  </div>
                </SelectItem>
                <SelectItem value="security">
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Security Analyst
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Agent Name */}
          <div className="space-y-2">
            <Label htmlFor="agent-name">Agent Name</Label>
            <Input
              id="agent-name"
              placeholder="e.g., Research Assistant"
              value={agentName}
              onChange={(e) => setAgentName(e.target.value)}
            />
          </div>

          {/* System Prompt */}
          <div className="space-y-2">
            <Label htmlFor="system-prompt">System Prompt (Optional)</Label>
            <Textarea
              id="system-prompt"
              placeholder="Define the agent's behavior and specialization..."
              value={systemPrompt}
              onChange={(e) => setSystemPrompt(e.target.value)}
              rows={3}
            />
          </div>

          {/* Capabilities */}
          <div className="space-y-2">
            <Label htmlFor="capabilities">Capabilities (comma-separated)</Label>
            <Input
              id="capabilities"
              placeholder="e.g., web-search, analysis, reporting"
              value={capabilities.join(', ')}
              onChange={(e) => handleCapabilitiesChange(e.target.value)}
            />
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label htmlFor="priority">Priority: {priority}</Label>
            <Slider
              id="priority"
              min={1}
              max={10}
              step={1}
              value={[priority]}
              onValueChange={(value) => setPriority(value[0])}
            />
          </div>

          {/* Max Concurrent Tasks */}
          <div className="space-y-2">
            <Label htmlFor="max-tasks">Max Concurrent Tasks: {maxConcurrentTasks}</Label>
            <Slider
              id="max-tasks"
              min={1}
              max={10}
              step={1}
              value={[maxConcurrentTasks]}
              onValueChange={(value) => setMaxConcurrentTasks(value[0])}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading || !agentName.trim()}>
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Spawning...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Spawn Agent
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

/**
 * Dialog for creating a new task
 */
const CreateTaskDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateTask: (config: any) => void;
  agents: ClaudeFlowAgent[];
  loading: boolean;
}> = ({ open, onOpenChange, onCreateTask, agents, loading }) => {
  const [taskType, setTaskType] = useState('research');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState(5);
  const [assignToAgent, setAssignToAgent] = useState('');
  const [assignToAgentType, setAssignToAgentType] = useState('');
  const [timeout, setTimeout] = useState(3600); // 1 hour default

  const handleSubmit = () => {
    if (!description.trim()) {
      return;
    }

    onCreateTask({
      type: taskType,
      description,
      priority,
      assignToAgent: assignToAgent || undefined,
      assignToAgentType: assignToAgentType || undefined,
      timeout: timeout > 0 ? timeout : undefined
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-green-500" />
            Create New Task
          </DialogTitle>
          <DialogDescription>
            Create a task to be processed by available agents
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Task Type */}
          <div className="space-y-2">
            <Label htmlFor="task-type">Task Type</Label>
            <Select value={taskType} onValueChange={setTaskType}>
              <SelectTrigger id="task-type">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="research">Research</SelectItem>
                <SelectItem value="implementation">Implementation</SelectItem>
                <SelectItem value="review">Review</SelectItem>
                <SelectItem value="analysis">Analysis</SelectItem>
                <SelectItem value="testing">Testing</SelectItem>
                <SelectItem value="documentation">Documentation</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe what needs to be done..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
            />
          </div>

          {/* Assignment */}
          <div className="space-y-2">
            <Label>Assignment (Optional)</Label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="assign-agent" className="text-sm text-muted-foreground">Specific Agent</Label>
                <Select value={assignToAgent} onValueChange={setAssignToAgent}>
                  <SelectTrigger id="assign-agent">
                    <SelectValue placeholder="Auto-assign" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Auto-assign</SelectItem>
                    {agents.filter(a => a.status !== 'terminated').map(agent => (
                      <SelectItem key={agent.id} value={agent.id}>
                        {agent.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="assign-type" className="text-sm text-muted-foreground">Agent Type</Label>
                <Select value={assignToAgentType} onValueChange={setAssignToAgentType}>
                  <SelectTrigger id="assign-type">
                    <SelectValue placeholder="Any type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Any type</SelectItem>
                    <SelectItem value="researcher">Researcher</SelectItem>
                    <SelectItem value="implementer">Implementer</SelectItem>
                    <SelectItem value="reviewer">Reviewer</SelectItem>
                    <SelectItem value="security">Security Analyst</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label htmlFor="task-priority">Priority: {priority}</Label>
            <Slider
              id="task-priority"
              min={1}
              max={10}
              step={1}
              value={[priority]}
              onValueChange={(value) => setPriority(value[0])}
            />
          </div>

          {/* Timeout */}
          <div className="space-y-2">
            <Label htmlFor="timeout">Timeout (seconds): {timeout > 0 ? timeout : 'No timeout'}</Label>
            <Slider
              id="timeout"
              min={0}
              max={7200} // 2 hours max
              step={300} // 5 minute increments
              value={[timeout]}
              onValueChange={(value) => setTimeout(value[0])}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading || !description.trim()}>
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Create Task
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Wrap the component with error boundary
const ClaudeFlowManager: React.FC<ClaudeFlowManagerProps> = (props) => {
  return (
    <ClaudeFlowErrorBoundary>
      <ClaudeFlowManagerInner {...props} />
    </ClaudeFlowErrorBoundary>
  );
};

export default ClaudeFlowManager;