import React, { useState, useEffect, useRef, useMemo } from "react";
import { motion } from "framer-motion";
import { 
  ArrowLeft,
  FolderOpen,
  GitBranch,
  Settings,
  Hash,
  AlertTriangle,
  Clock,
  StickyNote,
  Network,
  Layout,
  Bot,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { api, type Session, SessionMode } from "@/lib/api";
import { cn } from "@/lib/utils";
import { MessageList } from "./claude-code-session/MessageList";
import { FloatingPromptInput, type FloatingPromptInputRef } from "./FloatingPromptInput";
import { CheckpointSettings } from "./CheckpointSettings";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import type { ClaudeStreamMessage } from "./AgentExecution";
import { ModeToggle } from "./ModeSelector";
import { ModeIndicator } from "./ModeIndicator";
import { useSessionStore } from "@/stores/sessionStore";

// New panel system imports
// Panel imports removed

interface ClaudeCodeSessionProps {
  session?: Session;
  initialProjectPath?: string;
  onBack: () => void;
  onProjectSettings?: (projectPath: string) => void;
  className?: string;
  onStreamingChange?: (isStreaming: boolean, sessionId: string | null) => void;
}

/**
 * Inner session component that uses the panel context
 */
const ClaudeCodeSessionInner: React.FC<ClaudeCodeSessionProps> = ({
  session,
  initialProjectPath = "",
  onBack,
  className,
  onStreamingChange,
}) => {
  const [projectPath, setProjectPath] = useState(initialProjectPath || session?.project_path || "");
  const [messages, setMessages] = useState<ClaudeStreamMessage[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [effectiveSession, setEffectiveSession] = useState<Session | null>(session || null);
  const [, setIsLoading] = useState(false);
  const [, setError] = useState<string | null>(null);
  const [currentMode, setCurrentMode] = useState<SessionMode>(session?.mode || SessionMode.Code);
  const floatingPromptRef = useRef<FloatingPromptInputRef>(null);
  const [isFirstPrompt, setIsFirstPrompt] = useState(true);
  
  // Panel management removed
  
  // Old panel state - will be removed once all panels are migrated
  type ActivePanel = 'settings' | 'forkDialog' | 'slashCommandsSettings' | null;
  const [activePanel, setActivePanel] = useState<ActivePanel>(null);

  const [showLayoutManager, setShowLayoutManager] = useState(false);
  
  // Helper functions for backward compatibility
  const showSettings = activePanel === 'settings';
  const setShowSettings = (show: boolean) => setActivePanel(show ? 'settings' : null);
  const setShowForkDialog = (show: boolean) => setActivePanel(show ? 'forkDialog' : null);
  
  // Session store
  const sessionStore = useSessionStore();
  
  // Sync session from props
  useEffect(() => {
    if (session) {
      setEffectiveSession(session);
      setProjectPath(session.project_path);
      setCurrentMode(session.mode || SessionMode.Code);
    }
  }, [session]);
  
  // Load session messages on mount or session change
  useEffect(() => {
    if (effectiveSession) {
      loadSessionMessages();
    }
  }, [effectiveSession?.id]);
  
  // Load messages
  const loadSessionMessages = async () => {
    if (!effectiveSession) {
      console.log('[ClaudeCodeSessionWithPanels] No effective session, skipping message load');
      return;
    }
    
    console.log('[ClaudeCodeSessionWithPanels] Loading session messages for:', effectiveSession.id);
    
    try {
      const history = await api.loadSessionHistory(effectiveSession.id, effectiveSession.project_id);
      console.log('[ClaudeCodeSessionWithPanels] Loaded session history:', history);
      setMessages(history);
    } catch (err) {
      console.error("Failed to load session history:", err);
    }
  };
  
  // Create new session if needed
  const createSession = async (): Promise<Session | null> => {
    if (!projectPath) {
      setError("Please select a project directory");
      return null;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Start a new session
      const sessionId = await api.openNewSession(projectPath);
      // Create a session object
      const newSession: Session = {
        id: sessionId,
        project_id: projectPath,
        project_path: projectPath,
        mode: currentMode,
        created_at: Date.now(),
        mode_history: []
      };
      setEffectiveSession(newSession);
      
      // Load empty message history for new session
      setMessages([]);
      return newSession;
    } catch (err) {
      console.error("Failed to create session:", err);
      setError(err instanceof Error ? err.message : "Failed to create session");
      return null;
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle send message
  const handleSendMessage = async (prompt: string, modelChoice: "sonnet" | "opus" = "sonnet") => {
    const model = modelChoice === "opus" ? "claude-3-opus-20240229" : "claude-3-5-sonnet-20241022";
    if (!prompt.trim()) return;
    
    let sessionToUse = effectiveSession;
    if (!sessionToUse) {
      const newSession = await createSession();
      if (!newSession) return;
      sessionToUse = newSession;
    }
    
    const userMessage: ClaudeStreamMessage = {
      type: "user",
      role: "user",
      content: prompt,
      timestamp: new Date().toISOString(),
    };
    
    setMessages(prev => [...prev, userMessage]);
    setIsStreaming(true);
    onStreamingChange?.(true, sessionToUse.id);
    
    try {
      // Use the correct API method based on whether it's a new or existing session
      if (isFirstPrompt) {
        await api.executeClaudeCode(projectPath, prompt, model, currentMode);
        setIsFirstPrompt(false);
      } else {
        await api.continueClaudeCode(projectPath, prompt, model, currentMode);
      }
      
      // Messages will be received through the streaming event listener
    } catch (err) {
      console.error("Stream error:", err);
      setError(err instanceof Error ? err.message : "Failed to send message");
    } finally {
      setIsStreaming(false);
      onStreamingChange?.(false, sessionToUse.id);
    }
  };
  
  
  // Calculate dynamic layout based on open panels
  const layoutMargins = useMemo(() => {
    const leftPanels = panelManager.getPanelsInZone('left');
    const rightPanels = panelManager.getPanelsInZone('right');
    const bottomPanels = panelManager.getPanelsInZone('bottom');
    
    let marginLeft = 0;
    let marginRight = 0;
    let marginBottom = 0;
    
    // Calculate margins based on open panels
    leftPanels.forEach(panelId => {
      const panel = panelManager.panels[panelId];
      if (panel?.isOpen && !panel.isMinimized) {
        const width = parseInt(panel.size?.width || '384px');
        marginLeft = Math.max(marginLeft, width);
      }
    });
    
    rightPanels.forEach(panelId => {
      const panel = panelManager.panels[panelId];
      if (panel?.isOpen && !panel.isMinimized) {
        const width = parseInt(panel.size?.width || '384px');
        marginRight = Math.max(marginRight, width);
      }
    });
    
    bottomPanels.forEach(panelId => {
      const panel = panelManager.panels[panelId];
      if (panel?.isOpen && !panel.isMinimized) {
        const height = parseInt(panel.size?.height || '300px');
        marginBottom = Math.max(marginBottom, height);
      }
    });
    
    return { marginLeft, marginRight, marginBottom };
  }, [panelManager.panels]);
  
  // Render messages list
  const messagesList = (
    <div className="flex-1 overflow-hidden">
      <MessageList
         messages={messages}
         projectPath={projectPath}
         isStreaming={isStreaming}
         onLinkDetected={(url) => {
           console.log('Link detected:', url);
           // TODO: Handle link detection if needed
         }}
       />
    </div>
  );
  
  return (
    <div className={cn("h-full relative overflow-hidden", className)}>
      {/* Premium Background with Gradient Mesh */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 dark:from-slate-950 dark:via-slate-900 dark:to-blue-950/30" />
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/2 via-purple-500/2 to-cyan-500/2 opacity-50" />
      
      {/* Main Layout Grid */}
      <div 
        className="h-full grid grid-rows-[auto_1fr] relative z-10"
        style={{
          gridTemplateColumns: `${layoutMargins.marginLeft}px 1fr ${layoutMargins.marginRight}px`,
          transition: 'grid-template-columns 0.3s ease-in-out'
        }}
      >
        {/* Enhanced Premium Header - Spans all columns */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="col-span-3 relative bg-gradient-to-r from-white/90 via-white/95 to-white/90 dark:from-slate-900/90 dark:via-slate-800/95 dark:to-slate-900/90 backdrop-blur-xl border-b border-white/20 dark:border-slate-700/30 shadow-sm"
        >
          {/* Subtle gradient overlay for depth */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/3 via-purple-500/3 to-cyan-500/3 opacity-50" />
          
          <div className="relative z-10 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={onBack}
                        className="h-10 w-10 rounded-xl bg-gradient-to-br from-white/60 to-white/40 dark:from-slate-800/60 dark:to-slate-700/40 backdrop-blur-sm border border-white/20 dark:border-slate-600/20 hover:bg-white/80 dark:hover:bg-slate-700/60 hover:scale-105 transition-all duration-300 shadow-sm hover:shadow-md group"
                      >
                        <ArrowLeft className="h-4 w-4 text-slate-600 dark:text-slate-300 group-hover:text-slate-800 dark:group-hover:text-white transition-colors" />
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="bg-slate-900/95 backdrop-blur-sm border border-slate-700/50">
                    <p className="text-sm text-white">Back to Projects</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              {/* Enhanced Mode Toggle Container */}
              <div className="flex items-center gap-3 px-4 py-2 rounded-xl bg-gradient-to-r from-white/60 to-white/40 dark:from-slate-800/60 dark:to-slate-700/40 backdrop-blur-sm border border-white/20 dark:border-slate-600/20 shadow-sm">
                <ModeToggle 
                  sessionId={effectiveSession?.id || ''}
                  projectId={effectiveSession?.project_id || projectPath}
                  currentMode={currentMode}
                />
                <div className="w-px h-4 bg-slate-300/50 dark:bg-slate-600/50" />
                <ModeIndicator mode={currentMode} />
              </div>
            </div>
            
            {/* Enhanced Panel Toggles */}
            <div className="flex items-center gap-3">
              {/* Tool Palette */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => panelManager.togglePanel('toolPalette')}
                        className={cn(
                          "h-10 w-10 rounded-xl bg-gradient-to-br backdrop-blur-sm border transition-all duration-300 shadow-sm hover:shadow-md group",
                          panelManager.isPanelOpen('toolPalette')
                            ? "from-blue-500/20 to-purple-500/20 border-blue-500/30 bg-blue-500/10"
                            : "from-white/60 to-white/40 dark:from-slate-800/60 dark:to-slate-700/40 border-white/20 dark:border-slate-600/20 hover:bg-white/80 dark:hover:bg-slate-700/60"
                        )}
                      >
                        <Hash className={cn(
                          "h-4 w-4 transition-colors",
                          panelManager.isPanelOpen('toolPalette')
                            ? "text-blue-600 dark:text-blue-400"
                            : "text-slate-600 dark:text-slate-300 group-hover:text-slate-800 dark:group-hover:text-white"
                        )} />
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="bg-slate-900/95 backdrop-blur-sm border border-slate-700/50">
                    <p className="text-sm text-white">Tool Palette</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              {currentMode === SessionMode.Code && effectiveSession && (
                <>
                  {/* Architect Plans */}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => panelManager.togglePanel('planSidebar')}
                            className={cn(
                              "h-10 w-10 rounded-xl bg-gradient-to-br backdrop-blur-sm border transition-all duration-300 shadow-sm hover:shadow-md group",
                              panelManager.isPanelOpen('planSidebar')
                                ? "from-purple-500/20 to-pink-500/20 border-purple-500/30 bg-purple-500/10"
                                : "from-white/60 to-white/40 dark:from-slate-800/60 dark:to-slate-700/40 border-white/20 dark:border-slate-600/20 hover:bg-white/80 dark:hover:bg-slate-700/60"
                            )}
                          >
                            <FolderOpen className={cn(
                              "h-4 w-4 transition-colors",
                              panelManager.isPanelOpen('planSidebar')
                                ? "text-purple-600 dark:text-purple-400"
                                : "text-slate-600 dark:text-slate-300 group-hover:text-slate-800 dark:group-hover:text-white"
                            )} />
                          </Button>
                        </motion.div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="bg-slate-900/95 backdrop-blur-sm border border-slate-700/50">
                        <p className="text-sm text-white">Architect Plans</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  {/* Quality Validation */}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => panelManager.togglePanel('qualityValidation')}
                            className={cn(
                              "h-10 w-10 rounded-xl bg-gradient-to-br backdrop-blur-sm border transition-all duration-300 shadow-sm hover:shadow-md group",
                              panelManager.isPanelOpen('qualityValidation')
                                ? "from-yellow-500/20 to-orange-500/20 border-yellow-500/30 bg-yellow-500/10"
                                : "from-white/60 to-white/40 dark:from-slate-800/60 dark:to-slate-700/40 border-white/20 dark:border-slate-600/20 hover:bg-white/80 dark:hover:bg-slate-700/60"
                            )}
                          >
                            <AlertTriangle className={cn(
                              "h-4 w-4 transition-colors",
                              panelManager.isPanelOpen('qualityValidation')
                                ? "text-yellow-600 dark:text-yellow-400"
                                : "text-slate-600 dark:text-slate-300 group-hover:text-slate-800 dark:group-hover:text-white"
                            )} />
                          </Button>
                        </motion.div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="bg-slate-900/95 backdrop-blur-sm border border-slate-700/50">
                        <p className="text-sm text-white">Quality Validation</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </>
              )}
              
              {effectiveSession && (
                <div className="flex items-center gap-2 px-3 py-1.5 rounded-xl bg-gradient-to-r from-white/40 to-white/30 dark:from-slate-800/40 dark:to-slate-700/30 backdrop-blur-sm border border-white/20 dark:border-slate-600/20">
                  {/* Timeline */}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => panelManager.togglePanel('timeline')}
                            className={cn(
                              "h-8 w-8 rounded-lg bg-gradient-to-br backdrop-blur-sm border transition-all duration-300 group",
                              panelManager.isPanelOpen('timeline')
                                ? "from-blue-500/20 to-cyan-500/20 border-blue-500/30 bg-blue-500/10"
                                : "from-white/40 to-white/20 dark:from-slate-700/40 dark:to-slate-600/20 border-white/20 dark:border-slate-600/20 hover:bg-white/60 dark:hover:bg-slate-600/40"
                            )}
                          >
                            <GitBranch className={cn(
                              "h-3.5 w-3.5 transition-colors",
                              panelManager.isPanelOpen('timeline')
                                ? "text-blue-600 dark:text-blue-400"
                                : "text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-200"
                            )} />
                          </Button>
                        </motion.div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="bg-slate-900/95 backdrop-blur-sm border border-slate-700/50">
                        <p className="text-sm text-white">Timeline</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  {/* Mode History */}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => panelManager.togglePanel('modeHistory')}
                            className={cn(
                              "h-8 w-8 rounded-lg bg-gradient-to-br backdrop-blur-sm border transition-all duration-300 group",
                              panelManager.isPanelOpen('modeHistory')
                                ? "from-emerald-500/20 to-green-500/20 border-emerald-500/30 bg-emerald-500/10"
                                : "from-white/40 to-white/20 dark:from-slate-700/40 dark:to-slate-600/20 border-white/20 dark:border-slate-600/20 hover:bg-white/60 dark:hover:bg-slate-600/40"
                            )}
                          >
                            <Clock className={cn(
                              "h-3.5 w-3.5 transition-colors",
                              panelManager.isPanelOpen('modeHistory')
                                ? "text-emerald-600 dark:text-emerald-400"
                                : "text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-200"
                            )} />
                          </Button>
                        </motion.div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="bg-slate-900/95 backdrop-blur-sm border border-slate-700/50">
                        <p className="text-sm text-white">Mode History</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  {/* Session Notes */}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => panelManager.togglePanel('sessionNotes')}
                            className={cn(
                              "h-8 w-8 rounded-lg bg-gradient-to-br backdrop-blur-sm border transition-all duration-300 group",
                              panelManager.isPanelOpen('sessionNotes')
                                ? "from-amber-500/20 to-yellow-500/20 border-amber-500/30 bg-amber-500/10"
                                : "from-white/40 to-white/20 dark:from-slate-700/40 dark:to-slate-600/20 border-white/20 dark:border-slate-600/20 hover:bg-white/60 dark:hover:bg-slate-600/40"
                            )}
                          >
                            <StickyNote className={cn(
                              "h-3.5 w-3.5 transition-colors",
                              panelManager.isPanelOpen('sessionNotes')
                                ? "text-amber-600 dark:text-amber-400"
                                : "text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-200"
                            )} />
                          </Button>
                        </motion.div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="bg-slate-900/95 backdrop-blur-sm border border-slate-700/50">
                        <p className="text-sm text-white">Session Notes</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  {/* Session MCP */}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => panelManager.togglePanel('sessionMCPManager')}
                            className={cn(
                              "h-8 w-8 rounded-lg bg-gradient-to-br backdrop-blur-sm border transition-all duration-300 group",
                              panelManager.isPanelOpen('sessionMCPManager')
                                ? "from-indigo-500/20 to-purple-500/20 border-indigo-500/30 bg-indigo-500/10"
                                : "from-white/40 to-white/20 dark:from-slate-700/40 dark:to-slate-600/20 border-white/20 dark:border-slate-600/20 hover:bg-white/60 dark:hover:bg-slate-600/40"
                            )}
                          >
                            <Network className={cn(
                              "h-3.5 w-3.5 transition-colors",
                              panelManager.isPanelOpen('sessionMCPManager')
                                ? "text-indigo-600 dark:text-indigo-400"
                                : "text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-200"
                            )} />
                          </Button>
                        </motion.div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="bg-slate-900/95 backdrop-blur-sm border border-slate-700/50">
                        <p className="text-sm text-white">Session MCP</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  
                  {/* Session Agents */}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => panelManager.togglePanel('sessionAgents')}
                            className={cn(
                              "h-8 w-8 rounded-lg bg-gradient-to-br backdrop-blur-sm border transition-all duration-300 group",
                              panelManager.isPanelOpen('sessionAgents')
                                ? "from-pink-500/20 to-rose-500/20 border-pink-500/30 bg-pink-500/10"
                                : "from-white/40 to-white/20 dark:from-slate-700/40 dark:to-slate-600/20 border-white/20 dark:border-slate-600/20 hover:bg-white/60 dark:hover:bg-slate-600/40"
                            )}
                          >
                            <Bot className={cn(
                              "h-3.5 w-3.5 transition-colors",
                              panelManager.isPanelOpen('sessionAgents')
                                ? "text-pink-600 dark:text-pink-400"
                                : "text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-200"
                            )} />
                          </Button>
                        </motion.div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="bg-slate-900/95 backdrop-blur-sm border border-slate-700/50">
                        <p className="text-sm text-white">Session Agents</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              )}
              
              {/* Layout Manager */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setShowLayoutManager(true)}
                        className="h-10 w-10 rounded-xl bg-gradient-to-br from-white/60 to-white/40 dark:from-slate-800/60 dark:to-slate-700/40 backdrop-blur-sm border border-white/20 dark:border-slate-600/20 hover:bg-white/80 dark:hover:bg-slate-700/60 hover:scale-105 transition-all duration-300 shadow-sm hover:shadow-md group"
                      >
                        <Layout className="h-4 w-4 text-slate-600 dark:text-slate-300 group-hover:text-slate-800 dark:group-hover:text-white transition-colors" />
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="bg-slate-900/95 backdrop-blur-sm border border-slate-700/50">
                    <p className="text-sm text-white">Layout Manager</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              {/* Settings */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.div
                      whileHover={{ scale: 1.05, rotate: 90 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setShowSettings(true)}
                        className="h-10 w-10 rounded-xl bg-gradient-to-br from-white/60 to-white/40 dark:from-slate-800/60 dark:to-slate-700/40 backdrop-blur-sm border border-white/20 dark:border-slate-600/20 hover:bg-white/80 dark:hover:bg-slate-700/60 hover:scale-105 transition-all duration-300 shadow-sm hover:shadow-md group"
                      >
                        <Settings className="h-4 w-4 text-slate-600 dark:text-slate-300 group-hover:text-slate-800 dark:group-hover:text-white transition-all duration-300" />
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="bg-slate-900/95 backdrop-blur-sm border border-slate-700/50">
                    <p className="text-sm text-white">Settings</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          </div>
        </motion.div>
        
        {/* Left Panel Space */}
        <div className="row-start-2" />
        
        {/* Main Content Area */}
        <div 
          className="row-start-2 overflow-hidden relative"
          style={{
            marginBottom: `${layoutMargins.marginBottom}px`,
            transition: 'margin-bottom 0.3s ease-in-out'
          }}
        >
          {/* Content Container with Premium Glass Effect */}
          <div className="h-full relative p-4">
            {/* Subtle content background */}
            <div className="absolute inset-4 bg-gradient-to-br from-white/40 via-white/30 to-white/20 dark:from-slate-800/40 dark:via-slate-800/30 dark:to-slate-800/20 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-slate-700/20 shadow-xl" />
            
            <div className="relative z-10 h-full flex flex-col max-w-6xl mx-auto">
              <div className="h-full bg-gradient-to-br from-white/60 to-white/40 dark:from-slate-900/60 dark:to-slate-800/40 backdrop-blur-sm rounded-xl border border-white/20 dark:border-slate-700/20 overflow-hidden shadow-lg">
                {messagesList}
              </div>
            </div>
          </div>
          
          {/* Floating prompt */}
          <FloatingPromptInput
            ref={floatingPromptRef}
            onSend={handleSendMessage}
            onCancel={() => {}} // TODO: Implement cancel handling
            isLoading={isStreaming}
            disabled={!projectPath}
            projectPath={projectPath}
            className="absolute bottom-0 left-0 right-0"
          />
        </div>
        
        {/* Right Panel Space */}
        <div className="row-start-2" />
      </div>
      
      {/* Panel System - Positioned absolutely outside grid */}
      {/* Panel Dock for minimized panels */}
      <PanelDock position="left" />
      
      {/* Panels - Using new panel system */}
      {/* Tool Palette Panel */}
      <ToolPalettePanel mode={currentMode} />
      
      {/* Session-specific panels */}
      {effectiveSession && (
        <>
          <TimelineNavigatorPanel
            sessionId={effectiveSession.id}
            projectId={effectiveSession.project_id}
            projectPath={projectPath}
            currentMessageIndex={messages.length - 1}
            onCheckpointSelect={() => {
              // Handle checkpoint selection
              loadSessionMessages();
            }}
            onFork={(checkpointId) => {
              setForkCheckpointId(checkpointId);
              setShowForkDialog(true);
            }}
            refreshVersion={timelineVersion}
          />
          
          <PlanSidebarPanel
            projectId={effectiveSession.project_id}
            onPlanSelect={(plan) => {
              // Handle plan selection if needed
              console.log('Plan selected:', plan);
            }}
          />
          
          <QualityValidationPanel
            projectPath={projectPath}
            sessionMode={currentMode}
            onValidationComplete={(result) => {
              // Handle validation complete if needed
              console.log('Validation complete:', result);
            }}
          />
          
          <ModeHistoryPanel
            sessionId={effectiveSession.id}
            projectId={effectiveSession.project_id}
          />
          
          
          <SessionNotesPanel
            sessionId={effectiveSession.id}
          />
          
          <SessionMCPManagerPanel
            sessionId={effectiveSession.id}
          />
          
          <SessionAgentsManagerPanel
            sessionId={effectiveSession.id}
          />
        </>
      )}
        
        {/* Dialogs (not migrated yet) */}
        <Dialog open={showSettings} onOpenChange={setShowSettings}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Session Settings</DialogTitle>
            </DialogHeader>
            <CheckpointSettings
              sessionId={effectiveSession?.id || ''}
              projectId={effectiveSession?.project_id || projectPath}
              projectPath={projectPath}
              onClose={() => setShowSettings(false)}
            />
          </DialogContent>
        </Dialog>
        
      {/* Layout Manager Dialog */}
      <PanelLayoutManager
        open={showLayoutManager}
        onOpenChange={setShowLayoutManager}
      />
    </div>
  );
};

/**
 * ClaudeCodeSession component with new panel system
 */
export const ClaudeCodeSessionWithPanels: React.FC<ClaudeCodeSessionProps> = (props) => {
  return (
    <PanelProvider>
      <ClaudeCodeSessionInner {...props} />
    </PanelProvider>
  );
};

export default ClaudeCodeSessionWithPanels;