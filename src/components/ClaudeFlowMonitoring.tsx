import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from './ui/card';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { Button } from './ui/button';
import { 
  Activity, 
  Cpu, 
  MemoryStick, 
  Network, 
  Clock, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Download,
  Settings
} from 'lucide-react';
import { ClaudeFlowIntegration } from '@/lib/claudeFlowIntegration';
import { useClaudeFlowEvent } from '@/hooks/useClaudeFlowWebSocket';

interface SystemMetrics {
  timestamp: Date;
  cpu: number;
  memory: number;
  network: {
    latency: number;
    throughput: number;
    packetsPerSecond: number;
  };
  agents: {
    total: number;
    active: number;
    idle: number;
    error: number;
  };
  tasks: {
    total: number;
    running: number;
    completed: number;
    failed: number;
    queued: number;
  };
  performance: {
    avgResponseTime: number;
    successRate: number;
    errorRate: number;
    requestsPerSecond: number;
  };
}

interface Alert {
  id: string;
  type: 'warning' | 'error' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  resolved: boolean;
}

interface ClaudeFlowMonitoringProps {
  integration?: ClaudeFlowIntegration;
}

export const ClaudeFlowMonitoring: React.FC<ClaudeFlowMonitoringProps> = ({ integration }) => {
  const [metrics, setMetrics] = useState<SystemMetrics[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<SystemMetrics | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000); // 5 seconds

  // Subscribe to real-time metrics updates
  useClaudeFlowEvent<SystemMetrics>('metrics.updated', (metrics) => {
    console.log('Metrics updated:', metrics);
    setCurrentMetrics(metrics);
    setMetrics(prev => [...prev, metrics].slice(-60)); // Keep last 60 points
  });

  useClaudeFlowEvent<Alert>('alert.triggered', (alert) => {
    console.log('Alert triggered:', alert);
    setAlerts(prev => [alert, ...prev].slice(0, 10)); // Keep last 10 alerts
  });

  // Generate metrics (real or mock)
  const generateMetrics = async (): Promise<SystemMetrics> => {
    const now = new Date();
    
    // Try to get real metrics if integration is available
    if (integration) {
      try {
        const realMetrics = await integration.getSystemMetrics('1h');
        
        // Transform the API response to our SystemMetrics format
        const systemMetrics: SystemMetrics = {
          timestamp: now,
          cpu: realMetrics.cpu?.usage || 0,
          memory: realMetrics.memory?.usagePercent || 0,
          network: {
            latency: realMetrics.network?.latency || 0,
            throughput: realMetrics.network?.throughput || 0,
            packetsPerSecond: realMetrics.network?.packetsPerSecond || 0
          },
          agents: {
            total: realMetrics.agents?.total || 0,
            active: realMetrics.agents?.active || 0,
            idle: realMetrics.agents?.idle || 0,
            error: realMetrics.agents?.error || 0
          },
          tasks: {
            total: realMetrics.tasks?.total || 0,
            running: realMetrics.tasks?.running || 0,
            completed: realMetrics.tasks?.completed || 0,
            failed: realMetrics.tasks?.failed || 0,
            queued: realMetrics.tasks?.queued || 0
          },
          performance: {
            avgResponseTime: realMetrics.performance?.avgResponseTime || 0,
            successRate: realMetrics.performance?.successRate || 0,
            errorRate: realMetrics.performance?.errorRate || 0,
            requestsPerSecond: realMetrics.performance?.requestsPerSecond || 0
          }
        };
        
        return systemMetrics;
      } catch (err) {
        console.error('Failed to fetch real metrics, using simulated data:', err);
      }
    }
    
    // Fallback to simulated metrics
    const baseMetrics = currentMetrics || {
      timestamp: now,
      cpu: 45,
      memory: 60,
      network: { latency: 120, throughput: 15.5, packetsPerSecond: 250 },
      agents: { total: 3, active: 2, idle: 1, error: 0 },
      tasks: { total: 15, running: 2, completed: 10, failed: 1, queued: 2 },
      performance: { avgResponseTime: 1200, successRate: 0.973, errorRate: 0.027, requestsPerSecond: 8.5 }
    };

    // Add realistic variations
    const variation = (base: number, range: number) => 
      Math.max(0, Math.min(100, base + (Math.random() - 0.5) * range));

    const newMetrics: SystemMetrics = {
      timestamp: now,
      cpu: variation(baseMetrics.cpu, 15),
      memory: variation(baseMetrics.memory, 10),
      network: {
        latency: Math.max(50, baseMetrics.network.latency + (Math.random() - 0.5) * 100),
        throughput: Math.max(0, baseMetrics.network.throughput + (Math.random() - 0.5) * 5),
        packetsPerSecond: Math.max(0, baseMetrics.network.packetsPerSecond + (Math.random() - 0.5) * 50)
      },
      agents: {
        total: baseMetrics.agents.total + (Math.random() > 0.95 ? (Math.random() > 0.5 ? 1 : -1) : 0),
        active: Math.max(0, baseMetrics.agents.active + (Math.random() > 0.9 ? (Math.random() > 0.5 ? 1 : -1) : 0)),
        idle: Math.max(0, baseMetrics.agents.idle + (Math.random() > 0.9 ? (Math.random() > 0.5 ? 1 : -1) : 0)),
        error: Math.max(0, baseMetrics.agents.error + (Math.random() > 0.95 ? (Math.random() > 0.8 ? 1 : -1) : 0))
      },
      tasks: {
        total: Math.max(0, baseMetrics.tasks.total + (Math.random() > 0.8 ? (Math.random() > 0.5 ? 1 : 0) : 0)),
        running: Math.max(0, baseMetrics.tasks.running + (Math.random() - 0.5) * 2),
        completed: baseMetrics.tasks.completed + (Math.random() > 0.7 ? 1 : 0),
        failed: Math.max(0, baseMetrics.tasks.failed + (Math.random() > 0.95 ? 1 : 0)),
        queued: Math.max(0, baseMetrics.tasks.queued + (Math.random() - 0.5) * 3)
      },
      performance: {
        avgResponseTime: Math.max(500, baseMetrics.performance.avgResponseTime + (Math.random() - 0.5) * 400),
        successRate: Math.max(0.8, Math.min(1, baseMetrics.performance.successRate + (Math.random() - 0.5) * 0.05)),
        errorRate: Math.max(0, Math.min(0.2, baseMetrics.performance.errorRate + (Math.random() - 0.5) * 0.01)),
        requestsPerSecond: Math.max(0, baseMetrics.performance.requestsPerSecond + (Math.random() - 0.5) * 2)
      }
    };

    // Generate alerts based on thresholds
    const newAlerts: Alert[] = [];
    
    if (newMetrics.cpu > 80) {
      newAlerts.push({
        id: `cpu-${Date.now()}`,
        type: 'warning',
        title: 'High CPU Usage',
        message: `CPU usage is at ${newMetrics.cpu.toFixed(1)}%`,
        timestamp: now,
        resolved: false
      });
    }

    if (newMetrics.memory > 85) {
      newAlerts.push({
        id: `memory-${Date.now()}`,
        type: 'error',
        title: 'High Memory Usage',
        message: `Memory usage is at ${newMetrics.memory.toFixed(1)}%`,
        timestamp: now,
        resolved: false
      });
    }

    if (newMetrics.network.latency > 300) {
      newAlerts.push({
        id: `latency-${Date.now()}`,
        type: 'warning',
        title: 'High Network Latency',
        message: `Network latency is ${newMetrics.network.latency.toFixed(0)}ms`,
        timestamp: now,
        resolved: false
      });
    }

    if (newMetrics.agents.error > 0) {
      newAlerts.push({
        id: `agent-error-${Date.now()}`,
        type: 'error',
        title: 'Agent Errors Detected',
        message: `${newMetrics.agents.error} agent(s) in error state`,
        timestamp: now,
        resolved: false
      });
    }

    if (newAlerts.length > 0) {
      setAlerts(prev => [...newAlerts, ...prev].slice(0, 10)); // Keep last 10 alerts
    }

    return newMetrics;
  };

  // Initialize and start monitoring
  useEffect(() => {
    const initializeMetrics = async () => {
      const initialMetrics = await generateMetrics();
      setCurrentMetrics(initialMetrics);
      setMetrics([initialMetrics]);
    };
    
    initializeMetrics();

    const interval = setInterval(async () => {
      if (isMonitoring) {
        const newMetrics = await generateMetrics();
        setCurrentMetrics(newMetrics);
        setMetrics(prev => [...prev, newMetrics].slice(-60)); // Keep last 60 points (5 minutes at 5s intervals)
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [isMonitoring, refreshInterval, integration]);

  const toggleMonitoring = async () => {
    if (integration) {
      try {
        if (isMonitoring) {
          await integration.stopMetricsCollection();
        } else {
          await integration.startMetricsCollection(refreshInterval);
        }
      } catch (err) {
        console.error('Failed to toggle metrics collection:', err);
      }
    }
    setIsMonitoring(!isMonitoring);
  };

  const clearAlerts = () => {
    setAlerts([]);
  };

  const exportMetrics = () => {
    const data = {
      timestamp: new Date().toISOString(),
      metrics,
      alerts
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `claude-flow-metrics-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusColor = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return 'text-red-500';
    if (value >= thresholds.warning) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getProgressColor = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return 'bg-red-500';
    if (value >= thresholds.warning) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getTrend = (current: number, previous: number | undefined) => {
    if (!previous) return null;
    const change = current - previous;
    if (Math.abs(change) < 0.1) return null;
    return change > 0 ? 'up' : 'down';
  };

  const previousMetrics = metrics.length > 1 ? metrics[metrics.length - 2] : undefined;

  if (!currentMetrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${isMonitoring ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
            <span className="text-sm font-medium">
              {isMonitoring ? 'Monitoring Active' : 'Monitoring Paused'}
            </span>
          </div>
          <Badge variant="outline" className="text-xs">
            Update: {refreshInterval / 1000}s
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline" onClick={toggleMonitoring}>
            {isMonitoring ? 'Pause' : 'Resume'}
          </Button>
          <Button size="sm" variant="outline" onClick={exportMetrics}>
            <Download className="h-3 w-3 mr-1" />
            Export
          </Button>
          <Button size="sm" variant="outline">
            <Settings className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* System Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* CPU Usage */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <Cpu className="h-4 w-4 text-blue-500" />
                CPU Usage
              </div>
              {getTrend(currentMetrics.cpu, previousMetrics?.cpu) === 'up' ? (
                <TrendingUp className="h-3 w-3 text-red-500" />
              ) : getTrend(currentMetrics.cpu, previousMetrics?.cpu) === 'down' ? (
                <TrendingDown className="h-3 w-3 text-green-500" />
              ) : null}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(currentMetrics.cpu, { warning: 70, critical: 85 })}`}>
              {currentMetrics.cpu.toFixed(1)}%
            </div>
            <Progress 
              value={currentMetrics.cpu} 
              className="mt-2 h-2"
            />
          </CardContent>
        </Card>

        {/* Memory Usage */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <MemoryStick className="h-4 w-4 text-purple-500" />
                Memory Usage
              </div>
              {getTrend(currentMetrics.memory, previousMetrics?.memory) === 'up' ? (
                <TrendingUp className="h-3 w-3 text-red-500" />
              ) : getTrend(currentMetrics.memory, previousMetrics?.memory) === 'down' ? (
                <TrendingDown className="h-3 w-3 text-green-500" />
              ) : null}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(currentMetrics.memory, { warning: 75, critical: 90 })}`}>
              {currentMetrics.memory.toFixed(1)}%
            </div>
            <Progress 
              value={currentMetrics.memory} 
              className="mt-2 h-2"
            />
          </CardContent>
        </Card>

        {/* Network Latency */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <Network className="h-4 w-4 text-green-500" />
                Network Latency
              </div>
              {getTrend(currentMetrics.network.latency, previousMetrics?.network.latency) === 'up' ? (
                <TrendingUp className="h-3 w-3 text-red-500" />
              ) : getTrend(currentMetrics.network.latency, previousMetrics?.network.latency) === 'down' ? (
                <TrendingDown className="h-3 w-3 text-green-500" />
              ) : null}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(currentMetrics.network.latency, { warning: 200, critical: 500 })}`}>
              {currentMetrics.network.latency.toFixed(0)}ms
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {currentMetrics.network.throughput.toFixed(1)} req/s
            </div>
          </CardContent>
        </Card>

        {/* Response Time */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-orange-500" />
                Avg Response
              </div>
              {getTrend(currentMetrics.performance.avgResponseTime, previousMetrics?.performance.avgResponseTime) === 'up' ? (
                <TrendingUp className="h-3 w-3 text-red-500" />
              ) : getTrend(currentMetrics.performance.avgResponseTime, previousMetrics?.performance.avgResponseTime) === 'down' ? (
                <TrendingDown className="h-3 w-3 text-green-500" />
              ) : null}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(currentMetrics.performance.avgResponseTime, { warning: 2000, critical: 5000 })}`}>
              {currentMetrics.performance.avgResponseTime.toFixed(0)}ms
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {(currentMetrics.performance.successRate * 100).toFixed(1)}% success
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Agent Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-500" />
              Agent Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Total</span>
                  <span className="font-medium">{currentMetrics.agents.total}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Active</span>
                  <span className="font-medium text-green-500">{currentMetrics.agents.active}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Idle</span>
                  <span className="font-medium text-yellow-500">{currentMetrics.agents.idle}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Error</span>
                  <span className="font-medium text-red-500">{currentMetrics.agents.error}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Task Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Task Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Running</span>
                  <span className="font-medium text-blue-500">{currentMetrics.tasks.running}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Queued</span>
                  <span className="font-medium text-yellow-500">{currentMetrics.tasks.queued}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Completed</span>
                  <span className="font-medium text-green-500">{currentMetrics.tasks.completed}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Failed</span>
                  <span className="font-medium text-red-500">{currentMetrics.tasks.failed}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Alerts */}
      {alerts.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                Recent Alerts ({alerts.length})
              </CardTitle>
              <Button size="sm" variant="outline" onClick={clearAlerts}>
                Clear All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {alerts.slice(0, 5).map((alert) => (
                <div 
                  key={alert.id} 
                  className="flex items-start gap-3 p-3 rounded-lg bg-card border"
                >
                  <div className={`p-1 rounded-full ${
                    alert.type === 'error' ? 'bg-red-500/20' : 
                    alert.type === 'warning' ? 'bg-yellow-500/20' : 'bg-blue-500/20'
                  }`}>
                    <AlertTriangle className={`h-3 w-3 ${
                      alert.type === 'error' ? 'text-red-500' : 
                      alert.type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-sm">{alert.title}</h4>
                      <Badge variant={alert.type === 'error' ? 'destructive' : 'secondary'} className="text-xs">
                        {alert.type}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">{alert.message}</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {alert.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};