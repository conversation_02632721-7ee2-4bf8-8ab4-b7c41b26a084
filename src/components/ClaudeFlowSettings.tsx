import React, { useState, useEffect } from 'react';
import { 
  GitBranch, 
  Save, 
  RefreshCw,
  Server,
  Database,
  Zap,
  CheckCircle,
  AlertCircle,
  Loader2,
  MemoryStick,
  Network,
  Shield,
  Activity,
  Download,
  Upload
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';

interface ClaudeFlowConfig {
  enabled: boolean;
  host: string;
  port: number;
  apiKey: string;
  orchestrator: {
    maxAgents: number;
    maxTasks: number;
    memoryLimit: number;
    defaultTimeout: number;
  };
  agents: {
    autoSpawn: boolean;
    defaultSpecialization: string;
    idleTimeout: number;
    maxRetries: number;
  };
  monitoring: {
    enabled: boolean;
    metricsInterval: number;
    alertThresholds: {
      cpu: number;
      memory: number;
      errorRate: number;
    };
  };
  websocket: {
    enabled: boolean;
    reconnectAttempts: number;
    reconnectDelay: number;
  };
}

interface ClaudeFlowSettingsProps {
  setToast: (toast: { message: string; type: 'success' | 'error' }) => void;
  onChange?: (hasChanges: boolean, getSettings: () => ClaudeFlowConfig, save: () => Promise<void>) => void;
}

const DEFAULT_CONFIG: ClaudeFlowConfig = {
  enabled: false,
  host: 'localhost',
  port: 8765,
  apiKey: '',
  orchestrator: {
    maxAgents: 10,
    maxTasks: 100,
    memoryLimit: 512,
    defaultTimeout: 300000
  },
  agents: {
    autoSpawn: true,
    defaultSpecialization: 'generic',
    idleTimeout: 600000,
    maxRetries: 3
  },
  monitoring: {
    enabled: true,
    metricsInterval: 5000,
    alertThresholds: {
      cpu: 80,
      memory: 85,
      errorRate: 5
    }
  },
  websocket: {
    enabled: true,
    reconnectAttempts: 5,
    reconnectDelay: 3000
  }
};

export const ClaudeFlowSettings: React.FC<ClaudeFlowSettingsProps> = ({
  setToast,
  onChange
}) => {
  const [config, setConfig] = useState<ClaudeFlowConfig>(DEFAULT_CONFIG);
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'error' | 'testing'>('disconnected');

  // Load saved configuration
  useEffect(() => {
    const loadConfig = async () => {
      try {
        // Try to load from MCP server first if Claude Flow is enabled
        const localConfig = localStorage.getItem('claudeFlowConfig');
        let baseConfig = DEFAULT_CONFIG;
        
        if (localConfig) {
          const parsed = JSON.parse(localConfig);
          baseConfig = { ...DEFAULT_CONFIG, ...parsed };
        }

        // If Claude Flow is enabled, try to load from MCP server
        if (baseConfig.enabled && baseConfig.host && baseConfig.port) {
          try {
            const { ClaudeFlowIntegration } = await import('@/lib/claudeFlowIntegration');
            const { MCPClient } = await import('@/lib/mcpClient');
            
            const integration = new ClaudeFlowIntegration({
              host: baseConfig.host,
              port: baseConfig.port,
              autoStart: false
            });

            const mcpClient = new MCPClient({
              serverName: 'claude-flow',
              timeout: 5000
            });

            await integration.initialize(mcpClient);
            const serverConfig = await integration.loadConfiguration();
            
            if (serverConfig) {
              // Merge server config with local config, server takes precedence
              setConfig({ ...baseConfig, ...serverConfig });
              // Update localStorage to stay in sync
              localStorage.setItem('claudeFlowConfig', JSON.stringify({ ...baseConfig, ...serverConfig }));
            } else {
              setConfig(baseConfig);
            }
          } catch (err) {
            console.warn('Failed to load config from MCP server, using local config:', err);
            setConfig(baseConfig);
          }
        } else {
          setConfig(baseConfig);
        }
      } catch (err) {
        console.error('Failed to load Claude Flow configuration:', err);
      }
    };
    loadConfig();
  }, []);

  // Notify parent of changes
  useEffect(() => {
    if (onChange) {
      onChange(hasChanges, () => config, save);
    }
  }, [hasChanges, config, onChange]);

  const updateConfig = (updates: Partial<ClaudeFlowConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
    setHasChanges(true);
  };

  const updateOrchestratorConfig = (key: keyof ClaudeFlowConfig['orchestrator'], value: any) => {
    setConfig(prev => ({
      ...prev,
      orchestrator: { ...prev.orchestrator, [key]: value }
    }));
    setHasChanges(true);
  };

  const updateAgentsConfig = (key: keyof ClaudeFlowConfig['agents'], value: any) => {
    setConfig(prev => ({
      ...prev,
      agents: { ...prev.agents, [key]: value }
    }));
    setHasChanges(true);
  };

  const updateMonitoringConfig = (key: keyof ClaudeFlowConfig['monitoring'], value: any) => {
    setConfig(prev => ({
      ...prev,
      monitoring: { ...prev.monitoring, [key]: value }
    }));
    setHasChanges(true);
  };

  const updateAlertThreshold = (key: keyof ClaudeFlowConfig['monitoring']['alertThresholds'], value: number) => {
    setConfig(prev => ({
      ...prev,
      monitoring: {
        ...prev.monitoring,
        alertThresholds: {
          ...prev.monitoring.alertThresholds,
          [key]: value
        }
      }
    }));
    setHasChanges(true);
  };

  const updateWebSocketConfig = (key: keyof ClaudeFlowConfig['websocket'], value: any) => {
    setConfig(prev => ({
      ...prev,
      websocket: { ...prev.websocket, [key]: value }
    }));
    setHasChanges(true);
  };

  const save = async () => {
    setIsSaving(true);
    try {
      // Always save to localStorage first (for offline/fallback)
      localStorage.setItem('claudeFlowConfig', JSON.stringify(config));
      
      // If Claude Flow is enabled, also save to MCP server
      if (config.enabled && config.host && config.port) {
        try {
          const { ClaudeFlowIntegration } = await import('@/lib/claudeFlowIntegration');
          const { MCPClient } = await import('@/lib/mcpClient');
          
          const integration = new ClaudeFlowIntegration({
            host: config.host,
            port: config.port,
            autoStart: false
          });

          const mcpClient = new MCPClient({
            serverName: 'claude-flow',
            timeout: 5000
          });

          await integration.initialize(mcpClient);
          await integration.saveConfiguration(config);
          
          setToast({ 
            message: 'Claude Flow configuration saved to server and local storage', 
            type: 'success' 
          });
        } catch (err) {
          console.warn('Failed to save to MCP server, saved locally:', err);
          setToast({ 
            message: 'Configuration saved locally (server save failed)', 
            type: 'success' 
          });
        }
      } else {
        setToast({ 
          message: 'Claude Flow configuration saved locally', 
          type: 'success' 
        });
      }
      
      setHasChanges(false);
    } catch (err) {
      console.error('Failed to save Claude Flow configuration:', err);
      setToast({ message: 'Failed to save configuration', type: 'error' });
    } finally {
      setIsSaving(false);
    }
  };

  const exportConfig = async () => {
    try {
      let exportData;
      
      // If Claude Flow is enabled, try to export from MCP server
      if (config.enabled && config.host && config.port && connectionStatus === 'connected') {
        try {
          const { ClaudeFlowIntegration } = await import('@/lib/claudeFlowIntegration');
          const { MCPClient } = await import('@/lib/mcpClient');
          
          const integration = new ClaudeFlowIntegration({
            host: config.host,
            port: config.port,
            autoStart: false
          });

          const mcpClient = new MCPClient({
            serverName: 'claude-flow',
            timeout: 5000
          });

          await integration.initialize(mcpClient);
          exportData = await integration.exportConfiguration();
        } catch (err) {
          console.warn('Failed to export from MCP server, using local config:', err);
          exportData = {
            config,
            metadata: {
              version: '1.0.0',
              exportedAt: new Date().toISOString()
            }
          };
        }
      } else {
        exportData = {
          config,
          metadata: {
            version: '1.0.0',
            exportedAt: new Date().toISOString()
          }
        };
      }
      
      // Create and download the file
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `claude-flow-config-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      setToast({ message: 'Configuration exported successfully', type: 'success' });
    } catch (err) {
      console.error('Failed to export configuration:', err);
      setToast({ message: 'Failed to export configuration', type: 'error' });
    }
  };

  const importConfig = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      const importData = JSON.parse(text);
      
      // Validate the imported data structure
      if (!importData.config || typeof importData.config !== 'object') {
        throw new Error('Invalid configuration file format');
      }
      
      // Apply the imported configuration
      const importedConfig = { ...DEFAULT_CONFIG, ...importData.config };
      setConfig(importedConfig);
      setHasChanges(true);
      
      // If Claude Flow is enabled, also import to MCP server
      if (importedConfig.enabled && importedConfig.host && importedConfig.port) {
        try {
          const { ClaudeFlowIntegration } = await import('@/lib/claudeFlowIntegration');
          const { MCPClient } = await import('@/lib/mcpClient');
          
          const integration = new ClaudeFlowIntegration({
            host: importedConfig.host,
            port: importedConfig.port,
            autoStart: false
          });

          const mcpClient = new MCPClient({
            serverName: 'claude-flow',
            timeout: 5000
          });

          await integration.initialize(mcpClient);
          await integration.importConfiguration(importData);
          
          setToast({ message: 'Configuration imported successfully', type: 'success' });
        } catch (err) {
          console.warn('Failed to import to MCP server:', err);
          setToast({ message: 'Configuration imported locally (server import failed)', type: 'success' });
        }
      } else {
        setToast({ message: 'Configuration imported successfully', type: 'success' });
      }
    } catch (err) {
      console.error('Failed to import configuration:', err);
      setToast({ message: 'Failed to import configuration', type: 'error' });
    }
    
    // Reset the input
    event.target.value = '';
  };

  const testConnection = async () => {
    setIsTestingConnection(true);
    setConnectionStatus('testing');
    
    try {
      // Check if Claude Flow is enabled and configured
      if (!config.enabled) {
        throw new Error('Claude Flow is disabled. Enable it first.');
      }
      
      if (!config.host || !config.port) {
        throw new Error('Claude Flow host and port must be configured');
      }

      // Create a temporary integration instance for testing
      const { ClaudeFlowIntegration } = await import('@/lib/claudeFlowIntegration');
      const { MCPClient } = await import('@/lib/mcpClient');
      
      const testIntegration = new ClaudeFlowIntegration({
        host: config.host,
        port: config.port,
        autoStart: false
      });

      // Create MCP client for claude-flow server
      const mcpClient = new MCPClient({
        serverName: 'claude-flow',
        timeout: 5000 // 5 second timeout for connection test
      });

      // Initialize the integration
      await testIntegration.initialize(mcpClient);
      
      // Test the connection
      const result = await testIntegration.testConnection();
      
      if (result.success) {
        setConnectionStatus('connected');
        setToast({ 
          message: `${result.message}${result.details?.serverVersion ? ` (v${result.details.serverVersion})` : ''}`, 
          type: 'success' 
        });
        
        // Log capabilities if available
        if (result.details?.capabilities) {
          console.log('Claude Flow capabilities:', result.details.capabilities);
        }
      } else {
        throw new Error(result.message);
      }
    } catch (err) {
      setConnectionStatus('error');
      const errorMessage = err instanceof Error ? err.message : 'Failed to connect to Claude Flow';
      setToast({ message: errorMessage, type: 'error' });
      console.error('Connection test failed:', err);
    } finally {
      setIsTestingConnection(false);
    }
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'testing':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Connection Status */}
      <div className="flex items-center justify-between p-4 rounded-lg bg-card border border-border">
        <div className="flex items-center gap-3">
          <GitBranch className="h-5 w-5 text-orange-500" />
          <div>
            <h3 className="font-medium">Claude Flow Orchestration</h3>
            <p className="text-sm text-muted-foreground">
              Multi-agent orchestration and swarm intelligence
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getConnectionStatusIcon()}
          <Badge variant={connectionStatus === 'connected' ? 'success' : 'secondary'}>
            {connectionStatus}
          </Badge>
        </div>
      </div>

      {/* Enable/Disable */}
      <div className="flex items-center justify-between">
        <div className="space-y-0.5">
          <Label htmlFor="claude-flow-enabled">Enable Claude Flow</Label>
          <p className="text-sm text-muted-foreground">
            Activate multi-agent orchestration system
          </p>
        </div>
        <Switch
          id="claude-flow-enabled"
          checked={config.enabled}
          onCheckedChange={(checked) => updateConfig({ enabled: checked })}
        />
      </div>

      <Separator />

      {/* Connection Settings */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <Server className="h-4 w-4" />
          Connection Settings
        </h4>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="cf-host">Host</Label>
            <Input
              id="cf-host"
              value={config.host}
              onChange={(e) => updateConfig({ host: e.target.value })}
              placeholder="localhost"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="cf-port">Port</Label>
            <Input
              id="cf-port"
              type="number"
              value={config.port}
              onChange={(e) => updateConfig({ port: parseInt(e.target.value) || 8765 })}
              placeholder="8765"
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="cf-api-key">API Key (Optional)</Label>
          <Input
            id="cf-api-key"
            type="password"
            value={config.apiKey}
            onChange={(e) => updateConfig({ apiKey: e.target.value })}
            placeholder="Enter API key if required"
          />
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="outline"
            onClick={testConnection}
            disabled={isTestingConnection}
            className="w-full"
          >
            {isTestingConnection ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Testing...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Test Connection
              </>
            )}
          </Button>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={exportConfig}
              className="flex-1"
              title="Export configuration"
            >
              <Download className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              className="flex-1"
              title="Import configuration"
              onClick={() => document.getElementById('import-config')?.click()}
            >
              <Upload className="h-4 w-4" />
            </Button>
            <input
              id="import-config"
              type="file"
              accept=".json"
              className="hidden"
              onChange={importConfig}
            />
          </div>
        </div>
      </div>

      <Separator />

      {/* Orchestrator Settings */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <Zap className="h-4 w-4" />
          Orchestrator Configuration
        </h4>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="cf-max-agents">Max Agents</Label>
            <Input
              id="cf-max-agents"
              type="number"
              value={config.orchestrator.maxAgents}
              onChange={(e) => updateOrchestratorConfig('maxAgents', parseInt(e.target.value) || 10)}
              min="1"
              max="50"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="cf-max-tasks">Max Tasks</Label>
            <Input
              id="cf-max-tasks"
              type="number"
              value={config.orchestrator.maxTasks}
              onChange={(e) => updateOrchestratorConfig('maxTasks', parseInt(e.target.value) || 100)}
              min="10"
              max="1000"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="cf-memory-limit">Memory Limit (MB)</Label>
            <Input
              id="cf-memory-limit"
              type="number"
              value={config.orchestrator.memoryLimit}
              onChange={(e) => updateOrchestratorConfig('memoryLimit', parseInt(e.target.value) || 512)}
              min="128"
              max="4096"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="cf-timeout">Default Timeout (ms)</Label>
            <Input
              id="cf-timeout"
              type="number"
              value={config.orchestrator.defaultTimeout}
              onChange={(e) => updateOrchestratorConfig('defaultTimeout', parseInt(e.target.value) || 300000)}
              min="10000"
              step="10000"
            />
          </div>
        </div>
      </div>

      <Separator />

      {/* Agent Settings */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <Shield className="h-4 w-4" />
          Agent Configuration
        </h4>
        
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="cf-auto-spawn">Auto-spawn Agents</Label>
            <p className="text-sm text-muted-foreground">
              Automatically spawn agents as needed
            </p>
          </div>
          <Switch
            id="cf-auto-spawn"
            checked={config.agents.autoSpawn}
            onCheckedChange={(checked) => updateAgentsConfig('autoSpawn', checked)}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="cf-default-spec">Default Specialization</Label>
          <Select
            value={config.agents.defaultSpecialization}
            onValueChange={(value) => updateAgentsConfig('defaultSpecialization', value)}
          >
            <SelectTrigger id="cf-default-spec">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="generic">Generic</SelectItem>
              <SelectItem value="researcher">Researcher</SelectItem>
              <SelectItem value="implementer">Implementer</SelectItem>
              <SelectItem value="reviewer">Reviewer</SelectItem>
              <SelectItem value="tester">Tester</SelectItem>
              <SelectItem value="security">Security</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="cf-idle-timeout">Idle Timeout (ms)</Label>
            <Input
              id="cf-idle-timeout"
              type="number"
              value={config.agents.idleTimeout}
              onChange={(e) => updateAgentsConfig('idleTimeout', parseInt(e.target.value) || 600000)}
              min="60000"
              step="60000"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="cf-max-retries">Max Retries</Label>
            <Input
              id="cf-max-retries"
              type="number"
              value={config.agents.maxRetries}
              onChange={(e) => updateAgentsConfig('maxRetries', parseInt(e.target.value) || 3)}
              min="0"
              max="10"
            />
          </div>
        </div>
      </div>

      <Separator />

      {/* Monitoring Settings */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <Activity className="h-4 w-4" />
          Monitoring & Alerts
        </h4>
        
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="cf-monitoring">Enable Monitoring</Label>
            <p className="text-sm text-muted-foreground">
              Collect and display system metrics
            </p>
          </div>
          <Switch
            id="cf-monitoring"
            checked={config.monitoring.enabled}
            onCheckedChange={(checked) => updateMonitoringConfig('enabled', checked)}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="cf-metrics-interval">Metrics Interval (ms)</Label>
          <Input
            id="cf-metrics-interval"
            type="number"
            value={config.monitoring.metricsInterval}
            onChange={(e) => updateMonitoringConfig('metricsInterval', parseInt(e.target.value) || 5000)}
            min="1000"
            max="60000"
            step="1000"
          />
        </div>
        
        <div className="space-y-3">
          <Label>Alert Thresholds</Label>
          
          <div className="grid grid-cols-3 gap-3">
            <div className="space-y-1">
              <Label htmlFor="cf-cpu-threshold" className="text-xs">CPU %</Label>
              <Input
                id="cf-cpu-threshold"
                type="number"
                value={config.monitoring.alertThresholds.cpu}
                onChange={(e) => updateAlertThreshold('cpu', parseInt(e.target.value) || 80)}
                min="50"
                max="100"
              />
            </div>
            
            <div className="space-y-1">
              <Label htmlFor="cf-memory-threshold" className="text-xs">Memory %</Label>
              <Input
                id="cf-memory-threshold"
                type="number"
                value={config.monitoring.alertThresholds.memory}
                onChange={(e) => updateAlertThreshold('memory', parseInt(e.target.value) || 85)}
                min="50"
                max="100"
              />
            </div>
            
            <div className="space-y-1">
              <Label htmlFor="cf-error-threshold" className="text-xs">Error Rate %</Label>
              <Input
                id="cf-error-threshold"
                type="number"
                value={config.monitoring.alertThresholds.errorRate}
                onChange={(e) => updateAlertThreshold('errorRate', parseInt(e.target.value) || 5)}
                min="1"
                max="50"
              />
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* WebSocket Settings */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <Network className="h-4 w-4" />
          WebSocket Configuration
        </h4>
        
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="cf-websocket">Enable WebSocket</Label>
            <p className="text-sm text-muted-foreground">
              Real-time updates and notifications
            </p>
          </div>
          <Switch
            id="cf-websocket"
            checked={config.websocket.enabled}
            onCheckedChange={(checked) => updateWebSocketConfig('enabled', checked)}
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="cf-reconnect-attempts">Reconnect Attempts</Label>
            <Input
              id="cf-reconnect-attempts"
              type="number"
              value={config.websocket.reconnectAttempts}
              onChange={(e) => updateWebSocketConfig('reconnectAttempts', parseInt(e.target.value) || 5)}
              min="0"
              max="20"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="cf-reconnect-delay">Reconnect Delay (ms)</Label>
            <Input
              id="cf-reconnect-delay"
              type="number"
              value={config.websocket.reconnectDelay}
              onChange={(e) => updateWebSocketConfig('reconnectDelay', parseInt(e.target.value) || 3000)}
              min="1000"
              max="30000"
              step="1000"
            />
          </div>
        </div>
      </div>

      {/* Save Button */}
      {hasChanges && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="sticky bottom-0 bg-background/80 backdrop-blur-sm p-4 -mx-6 border-t"
        >
          <Button
            onClick={save}
            disabled={isSaving}
            className="w-full"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Claude Flow Settings
              </>
            )}
          </Button>
        </motion.div>
      )}
    </div>
  );
};

export default ClaudeFlowSettings;