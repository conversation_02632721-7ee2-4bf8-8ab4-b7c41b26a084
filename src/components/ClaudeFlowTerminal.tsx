import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { ScrollArea } from './ui/scroll-area';
import { Terminal, Send, Trash2, Save, Play, Square, RefreshCw } from 'lucide-react';
import { ClaudeFlowIntegration } from '@/lib/claudeFlowIntegration';

interface TerminalEntry {
  id: string;
  command: string;
  output: string;
  timestamp: Date;
  status: 'success' | 'error' | 'running';
  type: 'command' | 'system' | 'agent';
}

interface ClaudeFlowTerminalProps {
  className?: string;
  integration?: ClaudeFlowIntegration;
}

export const ClaudeFlowTerminal: React.FC<ClaudeFlowTerminalProps> = ({ className, integration }) => {
  const [entries, setEntries] = useState<TerminalEntry[]>([]);
  const [currentCommand, setCurrentCommand] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [sessionId] = useState(() => `session-${Date.now()}`);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  /**
   * Get help text
   */
  const getHelpText = async () => {
    return `Available Claude-Flow Commands:

Agent Management:
  agents list              - List all active agents
  agents spawn <type>      - Spawn a new agent
  agents stop <id>         - Stop an agent
  agents status <id>       - Get agent status

Task Management:
  tasks list               - List all tasks
  tasks create <desc>      - Create a new task
  tasks assign <id> <agent> - Assign task to agent
  tasks status             - Show task queue status

Memory Operations:
  memory query <query>     - Query agent memory
  memory stats             - Show memory statistics
  memory clear <agent>     - Clear agent memory

System Commands:
  status                   - Show system status
  logs <agent>             - Show agent logs
  config                   - Show configuration
  restart                  - Restart orchestration system
  clear                    - Clear terminal
  exit                     - Exit terminal session`;
  };

  /**
   * Format agents list
   */
  const formatAgentsList = (agents: any[]) => {
    if (!agents || agents.length === 0) {
      return 'No active agents found.';
    }
    
    let output = 'Active Agents:\n\nID           Name                Type        Status    Tasks   Uptime\n';
    output += '─'.repeat(70) + '\n';
    
    agents.forEach(agent => {
      output += `${agent.id.padEnd(12)} ${agent.name.padEnd(19)} ${agent.specialization.padEnd(11)} ${agent.status.padEnd(9)} ${(agent.activeTask ? '1' : '0').padEnd(7)} ${agent.uptime || 'N/A'}\n`;
    });
    
    output += `\nTotal: ${agents.length} agents`;
    return output;
  };

  /**
   * Format tasks list
   */
  const formatTasksList = (tasks: any[]) => {
    if (!tasks || tasks.length === 0) {
      return 'No tasks found.';
    }
    
    let output = 'Task Queue:\n\nID      Description                    Status     Agent       Priority  Created\n';
    output += '─'.repeat(80) + '\n';
    
    tasks.forEach(task => {
      const desc = task.description.length > 28 ? task.description.substring(0, 25) + '...' : task.description;
      output += `${task.id.padEnd(7)} ${desc.padEnd(30)} ${task.status.padEnd(10)} ${(task.assignedAgent || '-').padEnd(11)} ${task.priority.padEnd(9)} ${new Date(task.createdAt).toLocaleTimeString()}\n`;
    });
    
    const stats = {
      running: tasks.filter(t => t.status === 'running').length,
      queued: tasks.filter(t => t.status === 'queued').length,
      completed: tasks.filter(t => t.status === 'completed').length
    };
    
    output += `\nTotal: ${tasks.length} tasks (${stats.running} running, ${stats.queued} queued, ${stats.completed} completed)`;
    return output;
  };

  /**
   * Format memory results
   */
  const formatMemoryResults = (memories: any[]) => {
    if (!memories || memories.length === 0) {
      return 'No memory entries found matching your query.';
    }
    
    let output = `Found ${memories.length} memory entries:\n\n`;
    
    memories.forEach((memory, index) => {
      output += `${index + 1}. [${memory.type}] ${memory.content}\n`;
      output += `   Agent: ${memory.agentId} | Tags: ${memory.tags.join(', ') || 'none'}\n`;
      output += `   Time: ${new Date(memory.timestamp).toLocaleString()}\n\n`;
    });
    
    return output;
  };

  /**
   * Format system status
   */
  const formatSystemStatus = (status: any) => {
    return `Claude-Flow System Status:

Orchestrator: ${status.orchestrator.status} (PID: ${status.orchestrator.pid || 'N/A'})
Uptime: ${status.orchestrator.uptime || 'N/A'}

Agents: ${status.agents.total} active, ${status.agents.idle} idle
Tasks: ${status.tasks.queued} queued, ${status.tasks.running} running, ${status.tasks.completed} completed
Memory: ${status.memory.entriesCount} entries, ${status.memory.storageUsed || 'N/A'}

Performance:
  Avg Response Time: ${status.performance.avgResponseTime || 'N/A'}
  Success Rate: ${status.performance.successRate || 'N/A'}
  Error Rate: ${status.performance.errorRate || 'N/A'}

MCP Connection: ${status.mcpConnected ? 'Connected' : 'Disconnected'}`;
  };

  // Initialize with welcome message
  useEffect(() => {
    const welcomeEntry: TerminalEntry = {
      id: 'welcome',
      command: '',
      output: `Claude-Flow Terminal v1.0.0
Session ID: ${sessionId}
Type 'help' for available commands or use claude-flow commands directly.

✓ Claude-Flow orchestration system online
✓ MCP connection established
✓ Terminal session initialized

claude-flow@claudia:~$`,
      timestamp: new Date(),
      status: 'success',
      type: 'system'
    };
    setEntries([welcomeEntry]);
  }, [sessionId]);

  // Auto-scroll to bottom when new entries are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollElement) {
        scrollElement.scrollTop = scrollElement.scrollHeight;
      }
    }
  }, [entries]);

  // Focus input when component mounts
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const executeCommand = async (command: string) => {
    if (!command.trim()) return;

    const commandEntry: TerminalEntry = {
      id: `cmd-${Date.now()}`,
      command,
      output: '',
      timestamp: new Date(),
      status: 'running',
      type: 'command'
    };

    setEntries(prev => [...prev, commandEntry]);
    setIsRunning(true);
    setCurrentCommand('');

    try {
      let output = '';
      let status: 'success' | 'error' = 'success';

      // Check if we have MCP integration
      if (integration) {
        // Use real MCP command execution
        try {
          // Handle special commands that don't need MCP
          if (command.toLowerCase() === 'clear') {
            setEntries([]);
            return;
          } else if (command.toLowerCase() === 'help') {
            output = await getHelpText();
          } else {
            // Parse Claude Flow commands
            const [cmd, ...args] = command.split(' ');
            
            if (cmd === 'agents' && args[0] === 'list') {
              const agents = await integration.listAgents();
              output = formatAgentsList(agents);
            } else if (cmd === 'agents' && args[0] === 'spawn') {
              const agentType = args[1] || 'generic';
              const agent = await integration.spawnAgent({ 
                specialization: agentType as any,
                capabilities: ['task_execution', 'memory_access'],
                systemPrompt: `You are a ${agentType} agent.`
              });
              output = `Agent spawned successfully!\nID: ${agent.id}\nName: ${agent.name}\nType: ${agentType}\nStatus: ${agent.status}`;
            } else if (cmd === 'agents' && args[0] === 'stop') {
              const agentId = args[1];
              if (agentId) {
                await integration.terminateAgent(agentId);
                output = `Agent ${agentId} terminated successfully.`;
              } else {
                output = 'Error: Please specify an agent ID';
                status = 'error';
              }
            } else if (cmd === 'tasks' && args[0] === 'list') {
              const tasks = await integration.listTasks();
              output = formatTasksList(tasks);
            } else if (cmd === 'tasks' && args[0] === 'create') {
              const description = args.slice(1).join(' ');
              const task = await integration.createTask({
                description,
                priority: 'medium',
                requiredCapabilities: []
              });
              output = `Task created successfully!\nID: ${task.id}\nDescription: ${task.description}\nStatus: ${task.status}`;
            } else if (cmd === 'memory' && args[0] === 'query') {
              const query = args.slice(1).join(' ');
              const memories = await integration.queryMemory({ search: query, limit: 5 });
              output = formatMemoryResults(memories);
            } else if (cmd === 'status') {
              const status = await integration.getSystemStatus();
              output = formatSystemStatus(status);
            } else {
              // Execute as shell command through MCP
              const result = await integration.executeCommand({
                command: cmd,
                args: args,
                cwd: process.cwd()
              });
              output = result.output || result.error || 'Command executed';
              status = result.exitCode === 0 ? 'success' : 'error';
            }
          }
        } catch (err) {
          output = `Error: ${err instanceof Error ? err.message : 'Command execution failed'}`;
          status = 'error';
        }
      } else {
        // Fallback to simulation when no integration
        await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

        switch (command.toLowerCase().trim()) {
          case 'help':
            output = `Available Claude-Flow Commands:

Agent Management:
  agents list              - List all active agents
  agents spawn <type>      - Spawn a new agent
  agents stop <id>         - Stop an agent
  agents status <id>       - Get agent status

Task Management:
  tasks list               - List all tasks
  tasks create <desc>      - Create a new task
  tasks assign <id> <agent> - Assign task to agent
  tasks status             - Show task queue status

Memory Operations:
  memory query <query>     - Query agent memory
  memory stats             - Show memory statistics
  memory clear <agent>     - Clear agent memory

System Commands:
  status                   - Show system status
  logs <agent>             - Show agent logs
  config                   - Show configuration
  restart                  - Restart orchestration system
  clear                    - Clear terminal
  exit                     - Exit terminal session`;
          break;

        case 'status':
          output = `Claude-Flow System Status:

Orchestrator: ✓ Running (PID: 12345)
Uptime: 2h 15m 32s

Agents: 3 active, 1 idle
Tasks: 5 queued, 2 running, 12 completed
Memory: 156MB used, 89% efficiency

Performance:
  Avg Response Time: 1.2s
  Success Rate: 97.3%
  Error Rate: 0.8%

Network: Connected to MCP (latency: 45ms)
Storage: 234MB used, 2.1GB available`;
          break;

        case 'agents list':
          output = `Active Agents:

ID           Name                Type        Status    Tasks   Uptime
agent_001    Research Assistant  researcher  running   2       1h 45m
agent_002    Code Developer      implementer idle      0       2h 12m
agent_003    QA Specialist       tester      running   1       45m

Total: 3 agents (2 active, 1 idle)`;
          break;

        case 'tasks list':
          output = `Task Queue:

ID      Description                    Status     Agent       Priority  Created
task_01 Analyze authentication system running    agent_001   high      10:30
task_02 Review API endpoints          queued     -           medium    10:45
task_03 Write test cases              running    agent_003   high      11:00
task_04 Update documentation         queued     -           low       11:15
task_05 Performance optimization     queued     -           medium    11:30

Total: 5 tasks (2 running, 3 queued)`;
          break;

        case 'memory stats':
          output = `Memory System Statistics:

Total Entries: 1,247
Active Contexts: 23
Storage Used: 89MB

By Type:
  Conversations: 856 entries (68.6%)
  Insights: 234 entries (18.8%)
  Patterns: 89 entries (7.1%)
  Errors: 68 entries (5.5%)

By Agent:
  agent_001: 542 entries
  agent_002: 398 entries
  agent_003: 307 entries

Memory Efficiency: 94.2%
Retrieval Speed: avg 12ms`;
          break;

        case 'config':
          output = `Claude-Flow Configuration:

System:
  Version: 1.0.0
  Node.js: v18.17.0
  MCP Protocol: v1.0
  
Orchestrator:
  Max Agents: 10
  Task Queue Size: 100
  Memory Limit: 512MB
  
API Keys:
  Anthropic: ✓ Configured
  OpenAI: ✓ Configured
  Perplexity: ✓ Configured
  
Network:
  Host: localhost
  Port: 8765
  MCP Port: 3000
  
Storage:
  Data Path: ~/.claude-flow/data
  Logs Path: ~/.claude-flow/logs
  Config Path: ~/.claude-flow/config.json`;
          break;

        case 'clear':
          setEntries([]);
          return;

        case 'exit':
          output = `Terminating Claude-Flow terminal session...
Session ${sessionId} ended.

Thank you for using Claude-Flow!`;
          break;

        default:
          if (command.startsWith('agents spawn ')) {
            const agentType = command.split(' ')[2] || 'generic';
            output = `Spawning new ${agentType} agent...

✓ Agent configuration loaded
✓ System prompt initialized
✓ Capabilities registered
✓ Memory system connected

Agent spawned successfully!
ID: agent_${Date.now().toString().slice(-3)}
Type: ${agentType}
Status: active
Ready to accept tasks.`;
          } else if (command.startsWith('tasks create ')) {
            const taskDesc = command.split(' ').slice(2).join(' ');
            output = `Creating new task: "${taskDesc}"

✓ Task validated
✓ Priority assessed: medium
✓ Added to queue

Task created successfully!
ID: task_${Date.now().toString().slice(-2)}
Status: queued
Estimated completion: 15-30 minutes`;
          } else if (command.startsWith('memory query ')) {
            const query = command.split(' ').slice(2).join(' ');
            output = `Querying agent memory for: "${query}"

Searching 1,247 memory entries...

Results found (3 matches):
1. [Insight] Authentication patterns should use JWT with refresh tokens
2. [Pattern] API rate limiting improves system stability
3. [Conversation] User reported login issues on mobile devices

Query completed in 23ms`;
          } else {
            output = `claude-flow: command not found: ${command}
Type 'help' for available commands.`;
            status = 'error';
          }
        }
      }

      // Update the command entry with results
      setEntries(prev => prev.map(entry => 
        entry.id === commandEntry.id 
          ? { ...entry, output, status, type: command.toLowerCase() === 'exit' ? 'system' : 'command' }
          : entry
      ));

    } catch (error) {
      setEntries(prev => prev.map(entry => 
        entry.id === commandEntry.id 
          ? { 
              ...entry, 
              output: `Error executing command: ${error instanceof Error ? error.message : 'Unknown error'}`, 
              status: 'error' as const 
            }
          : entry
      ));
    } finally {
      setIsRunning(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (currentCommand.trim() && !isRunning) {
      executeCommand(currentCommand.trim());
    }
  };

  const clearTerminal = () => {
    setEntries([]);
  };

  const saveSession = () => {
    const sessionData = {
      sessionId,
      timestamp: new Date().toISOString(),
      entries: entries.map(entry => ({
        command: entry.command,
        output: entry.output,
        timestamp: entry.timestamp.toISOString(),
        status: entry.status
      }))
    };

    const blob = new Blob([JSON.stringify(sessionData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `claude-flow-session-${sessionId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusIcon = (status: TerminalEntry['status']) => {
    switch (status) {
      case 'success': return '✓';
      case 'error': return '✗';
      case 'running': return '⟳';
    }
  };

  const getStatusColor = (status: TerminalEntry['status']) => {
    switch (status) {
      case 'success': return 'text-green-400';
      case 'error': return 'text-red-400';
      case 'running': return 'text-yellow-400';
    }
  };

  return (
    <Card className={`h-full ${className || ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Terminal className="h-5 w-5 text-green-500" />
            Claude-Flow Terminal
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {sessionId}
            </Badge>
            <Button size="sm" variant="outline" onClick={clearTerminal}>
              <Trash2 className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="outline" onClick={saveSession}>
              <Save className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0 h-full flex flex-col">
        {/* Terminal Output */}
        <ScrollArea ref={scrollAreaRef} className="flex-1 p-4">
          <div className="font-mono text-sm space-y-2">
            {entries.map((entry) => (
              <div key={entry.id} className="space-y-1">
                {entry.command && (
                  <div className="flex items-center gap-2">
                    <span className="text-blue-400">claude-flow@claudia:~$</span>
                    <span className="text-white">{entry.command}</span>
                    <span className={`text-xs ${getStatusColor(entry.status)}`}>
                      {getStatusIcon(entry.status)}
                    </span>
                  </div>
                )}
                {entry.output && (
                  <div className={`whitespace-pre-wrap pl-4 ${
                    entry.status === 'error' ? 'text-red-300' : 
                    entry.type === 'system' ? 'text-blue-300' : 'text-gray-300'
                  }`}>
                    {entry.output}
                  </div>
                )}
              </div>
            ))}
            {isRunning && (
              <div className="flex items-center gap-2 text-yellow-400">
                <RefreshCw className="h-3 w-3 animate-spin" />
                <span className="text-sm">Executing command...</span>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Command Input */}
        <div className="border-t p-4">
          <form onSubmit={handleSubmit} className="flex items-center gap-2">
            <span className="text-blue-400 font-mono text-sm flex-shrink-0">
              claude-flow@claudia:~$
            </span>
            <Input
              ref={inputRef}
              value={currentCommand}
              onChange={(e) => setCurrentCommand(e.target.value)}
              placeholder="Enter claude-flow command..."
              disabled={isRunning}
              className="flex-1 font-mono text-sm bg-transparent border-0 focus:ring-0 p-0"
            />
            <Button 
              type="submit" 
              size="sm" 
              disabled={!currentCommand.trim() || isRunning}
              className="flex-shrink-0"
            >
              {isRunning ? (
                <RefreshCw className="h-3 w-3 animate-spin" />
              ) : (
                <Send className="h-3 w-3" />
              )}
            </Button>
          </form>
          <div className="mt-2 text-xs text-gray-500">
            Type 'help' for available commands • Ctrl+C to interrupt • 'clear' to clear terminal
          </div>
        </div>
      </CardContent>
    </Card>
  );
};