import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Play, 
  Pause, 
  Square, 
  Upload,
  Download,
  GitBranch,
  CheckCircle,
  XCircle,
  Clock,
  Loader2,
  AlertCircle,
  FileText,
  ChevronRight,
  User,
  Zap,
  Shield,
  Database,
  Code,
  TestTube,
  Gauge,
  Package,
  BookOpen
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { 
  WorkflowEngine, 
  WorkflowDefinition, 
  WorkflowExecutionContext, 
  WorkflowAgent,
  createWorkflowEngine 
} from "@/lib/workflowEngine";
import { ClaudeFlowIntegration } from "@/lib/claudeFlowIntegration";
import { 
  useClaudeFlowWorkflowUpdates,
  useClaudeFlowAgentUpdates 
} from "@/hooks/useClaudeFlowWebSocket";

interface ClaudeFlowWorkflowProps {
  integration: ClaudeFlowIntegration;
}

/**
 * Claude Flow Workflow Component
 * Manages and visualizes workflow execution
 */
export const ClaudeFlowWorkflow: React.FC<ClaudeFlowWorkflowProps> = ({
  integration
}) => {
  const [engine] = useState(() => createWorkflowEngine(integration));
  const [workflows, setWorkflows] = useState<WorkflowDefinition[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<WorkflowDefinition | null>(null);
  const [activeExecution, setActiveExecution] = useState<WorkflowExecutionContext | null>(null);
  const [executionProgress, setExecutionProgress] = useState(0);
  const [executionMessage, setExecutionMessage] = useState("");
  const [showInputDialog, setShowInputDialog] = useState(false);
  const [workflowInputs, setWorkflowInputs] = useState<Record<string, any>>({});
  const [executionLog, setExecutionLog] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [workflowPhaseStatus, setWorkflowPhaseStatus] = useState<Record<string, 'pending' | 'running' | 'completed' | 'error'>>({});
  const [agentStatus, setAgentStatus] = useState<Record<string, 'idle' | 'running' | 'completed' | 'error'>>({});

  // Subscribe to workflow updates via WebSocket
  useClaudeFlowWorkflowUpdates((data, eventType) => {
    console.log('Workflow update:', eventType, data);
    
    switch (eventType) {
      case 'workflow.started':
        addToLog(`🚀 Workflow started: ${data.workflowId}`);
        setActiveExecution(prev => prev ? { ...prev, status: 'running' } : null);
        break;
        
      case 'workflow.phase.completed':
        addToLog(`✅ Phase completed: ${data.phaseName}`);
        setWorkflowPhaseStatus(prev => ({
          ...prev,
          [data.phaseName]: 'completed'
        }));
        // Update progress based on phase completion
        if (selectedWorkflow) {
          const phases = selectedWorkflow.execution_flow.phases;
          const completedIndex = phases.findIndex(p => p.name === data.phaseName);
          const progress = ((completedIndex + 1) / phases.length) * 100;
          setExecutionProgress(progress);
          setExecutionMessage(`Phase ${data.phaseName} completed`);
        }
        break;
        
      case 'workflow.completed':
        addToLog(`🎉 Workflow completed successfully!`);
        setActiveExecution(prev => prev ? { ...prev, status: 'completed' } : null);
        setExecutionProgress(100);
        setExecutionMessage('Workflow completed');
        break;
        
      case 'workflow.failed':
        addToLog(`❌ Workflow failed: ${data.error}`);
        setActiveExecution(prev => prev ? { ...prev, status: 'failed' } : null);
        setError(data.error);
        break;
    }
  });

  // Subscribe to agent updates via WebSocket
  useClaudeFlowAgentUpdates((agent, eventType) => {
    console.log('Agent update:', eventType, agent);
    
    switch (eventType) {
      case 'agent.spawned':
        addToLog(`🤖 Agent spawned: ${agent.name} (${agent.id})`);
        setAgentStatus(prev => ({ ...prev, [agent.id]: 'idle' }));
        break;
        
      case 'agent.updated':
        if (agent.status === 'running') {
          addToLog(`⚡ Agent working: ${agent.name}`);
          setAgentStatus(prev => ({ ...prev, [agent.id]: 'running' }));
        } else if (agent.status === 'idle') {
          addToLog(`✅ Agent completed task: ${agent.name}`);
          setAgentStatus(prev => ({ ...prev, [agent.id]: 'completed' }));
        }
        break;
        
      case 'agent.terminated':
        addToLog(`🛑 Agent terminated: ${agent.name}`);
        setAgentStatus(prev => ({ ...prev, [agent.id]: 'error' }));
        break;
    }
  });

  // Load available workflows
  useEffect(() => {
    loadWorkflows();
  }, []);

  /**
   * Load workflow definitions
   */
  const loadWorkflows = async () => {
    try {
      // In a real implementation, this would load from a directory or API
      // For now, we'll create a sample workflow based on the full-stack-feature workflow
      const sampleWorkflow: WorkflowDefinition = {
        workflow: {
          name: "Full-Stack Feature Development",
          description: "Complete feature development from requirements to production deployment",
          version: "1.0",
          estimated_duration: "2-5 days",
          complexity: "high"
        },
        agents: [
          {
            id: "business-analyst",
            name: "Business Analyst",
            phase: "requirements",
            dependencies: [],
            inputs: ["feature_request", "business_context"],
            outputs: ["requirements_document", "acceptance_criteria", "user_stories"],
            task_template: "Analyze the feature request: {feature_request}. Create detailed requirements including user stories, acceptance criteria, and business value. Consider edge cases and integration points."
          },
          {
            id: "backend-architect",
            name: "Backend Architect", 
            phase: "design",
            dependencies: ["business-analyst"],
            inputs: ["requirements_document", "existing_architecture"],
            outputs: ["api_design", "database_schema", "service_architecture"],
            task_template: "Based on requirements: {requirements_document}, design the backend architecture. Create API endpoints, database schema, and service boundaries. Consider scalability and integration patterns."
          },
          {
            id: "frontend-developer",
            name: "Frontend Developer",
            phase: "implementation",
            dependencies: ["backend-architect"],
            inputs: ["api_design", "user_stories", "ui_requirements"],
            outputs: ["frontend_implementation", "ui_components", "integration_code"],
            task_template: "Build the frontend interface based on user stories: {user_stories} and API design: {api_design}. Create responsive, accessible components with proper state management."
          },
          {
            id: "test-automator",
            name: "Test Automator",
            phase: "testing",
            dependencies: ["frontend-developer"],
            inputs: ["frontend_implementation", "acceptance_criteria"],
            outputs: ["test_suite", "integration_tests", "e2e_tests"],
            task_template: "Create comprehensive tests for frontend: {frontend_implementation}. Include unit tests, integration tests, and end-to-end tests covering all acceptance criteria."
          },
          {
            id: "deployment-engineer",
            name: "Deployment Engineer",
            phase: "deployment",
            dependencies: ["test-automator"],
            inputs: ["frontend_implementation", "infrastructure_requirements"],
            outputs: ["deployment_pipeline", "infrastructure_config", "monitoring_setup"],
            task_template: "Create deployment pipeline for the feature. Set up CI/CD, configure infrastructure, and implement monitoring. Ensure smooth production deployment with rollback capabilities."
          }
        ],
        execution_flow: {
          phases: [
            {
              name: "requirements",
              agents: ["business-analyst"],
              parallel: false
            },
            {
              name: "design", 
              agents: ["backend-architect"],
              parallel: false
            },
            {
              name: "implementation",
              agents: ["frontend-developer"],
              parallel: false
            },
            {
              name: "testing",
              agents: ["test-automator"],
              parallel: false
            },
            {
              name: "deployment",
              agents: ["deployment-engineer"],
              parallel: false
            }
          ]
        },
        quality_gates: [
          {
            phase: "design",
            criteria: ["Architecture review passed", "Database design validated"],
            reviewers: ["architect-review"]
          },
          {
            phase: "implementation", 
            criteria: ["Code review passed", "Unit tests > 90% coverage"],
            reviewers: ["code-reviewer"]
          },
          {
            phase: "deployment",
            criteria: ["All tests passing", "Performance benchmarks met"],
            reviewers: ["deployment-engineer"]
          }
        ]
      };
      
      setWorkflows([sampleWorkflow]);
    } catch (err) {
      console.error('Failed to load workflows:', err);
      setError('Failed to load workflow definitions');
    }
  };

  /**
   * Start workflow execution
   */
  const startWorkflow = async () => {
    if (!selectedWorkflow) return;

    setError(null);
    setExecutionLog([]);
    setActiveExecution(null);
    setWorkflowPhaseStatus({});
    setAgentStatus({});
    setExecutionProgress(0);
    setExecutionMessage('Initializing workflow...');

    try {
      const context = await engine.executeWorkflow(selectedWorkflow, {
        inputs: workflowInputs,
        onPhaseComplete: (phase, results) => {
          addToLog(`✅ Phase completed: ${phase}`);
          console.log('Phase results:', results);
        },
        onAgentComplete: (agentId, results) => {
          addToLog(`✅ Agent completed: ${agentId}`);
        },
        onError: (error) => {
          addToLog(`❌ Error: ${error}`);
          setError(error);
        },
        onProgress: (progress, message) => {
          setExecutionProgress(progress);
          setExecutionMessage(message);
          addToLog(`📊 Progress: ${progress.toFixed(1)}% - ${message}`);
        }
      });

      setActiveExecution(context);
      addToLog(`✅ Workflow completed successfully!`);
    } catch (err) {
      console.error('Workflow execution failed:', err);
      setError(err instanceof Error ? err.message : 'Workflow execution failed');
    }
  };

  /**
   * Add message to execution log
   */
  const addToLog = (message: string) => {
    setExecutionLog(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`]);
  };

  /**
   * Get agent icon based on type
   */
  const getAgentIcon = (agentId: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      'business-analyst': <User className="h-4 w-4" />,
      'backend-architect': <GitBranch className="h-4 w-4" />,
      'database-optimizer': <Database className="h-4 w-4" />,
      'python-pro': <Code className="h-4 w-4" />,
      'frontend-developer': <Code className="h-4 w-4" />,
      'test-automator': <TestTube className="h-4 w-4" />,
      'security-auditor': <Shield className="h-4 w-4" />,
      'performance-engineer': <Gauge className="h-4 w-4" />,
      'deployment-engineer': <Package className="h-4 w-4" />,
      'api-documenter': <BookOpen className="h-4 w-4" />
    };
    
    return iconMap[agentId] || <Zap className="h-4 w-4" />;
  };

  /**
   * Get phase status
   */
  const getPhaseStatus = (phaseName: string): 'pending' | 'running' | 'completed' | 'error' => {
    // Check WebSocket-driven status first
    if (workflowPhaseStatus[phaseName]) {
      return workflowPhaseStatus[phaseName];
    }
    
    if (!activeExecution) return 'pending';
    
    const phaseIndex = selectedWorkflow?.execution_flow.phases.findIndex(p => p.name === phaseName) || -1;
    const currentPhaseIndex = selectedWorkflow?.execution_flow.phases.findIndex(p => p.name === activeExecution.currentPhase) || -1;
    
    if (activeExecution.status === 'failed') return 'error';
    if (phaseIndex < currentPhaseIndex) return 'completed';
    if (phaseIndex === currentPhaseIndex) return 'running';
    return 'pending';
  };

  /**
   * Get phase badge variant
   */
  const getPhaseBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'running': return 'default';
      case 'error': return 'destructive';
      default: return 'secondary';
    }
  };

  /**
   * Handle workflow selection
   */
  const handleWorkflowSelect = (workflow: WorkflowDefinition) => {
    setSelectedWorkflow(workflow);
    setShowInputDialog(true);
    
    // Initialize inputs based on workflow requirements
    const requiredInputs: Record<string, any> = {};
    workflow.agents.forEach(agent => {
      agent.inputs.forEach(input => {
        if (!requiredInputs[input]) {
          requiredInputs[input] = '';
        }
      });
    });
    setWorkflowInputs(requiredInputs);
  };

  return (
    <Card className="border-orange-500/20 bg-gradient-to-br from-orange-500/5 to-purple-500/5">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <GitBranch className="h-5 w-5 text-orange-500" />
          Workflow Orchestration
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="workflows" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="workflows">Workflows</TabsTrigger>
            <TabsTrigger value="execution">Execution</TabsTrigger>
            <TabsTrigger value="log">Execution Log</TabsTrigger>
          </TabsList>

          {/* Workflows Tab */}
          <TabsContent value="workflows" className="space-y-4">
            <div className="space-y-4">
              {workflows.map((workflow, index) => (
                <div 
                  key={index}
                  className="p-4 rounded-lg bg-card/50 border border-border/20 hover:border-orange-500/20 transition-colors cursor-pointer"
                  onClick={() => handleWorkflowSelect(workflow)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium mb-1">{workflow.workflow.name}</h3>
                      <p className="text-sm text-muted-foreground mb-2">
                        {workflow.workflow.description}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>Version: {workflow.workflow.version}</span>
                        <span>Duration: {workflow.workflow.estimated_duration}</span>
                        <Badge variant="outline" className="text-xs">
                          {workflow.workflow.complexity}
                        </Badge>
                      </div>
                    </div>
                    <Button size="sm" className="gap-2">
                      <Play className="h-3 w-3" />
                      Execute
                    </Button>
                  </div>
                  
                  {/* Workflow phases preview */}
                  <div className="mt-4 flex items-center gap-2 flex-wrap">
                    {workflow.execution_flow.phases.map((phase, phaseIndex) => (
                      <div key={phase.name} className="flex items-center gap-1">
                        <Badge variant="secondary" className="text-xs">
                          {phase.name}
                        </Badge>
                        {phaseIndex < workflow.execution_flow.phases.length - 1 && (
                          <ChevronRight className="h-3 w-3 text-muted-foreground" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {workflows.length === 0 && (
              <div className="text-center py-12">
                <GitBranch className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Workflows Available</h3>
                <p className="text-muted-foreground mb-4">Upload workflow definitions to get started</p>
                <Button className="gap-2">
                  <Upload className="h-4 w-4" />
                  Upload Workflow
                </Button>
              </div>
            )}
          </TabsContent>

          {/* Execution Tab */}
          <TabsContent value="execution" className="space-y-4">
            {selectedWorkflow ? (
              <div className="space-y-4">
                {/* Workflow Header */}
                <div className="p-4 rounded-lg bg-card/50 border border-border/20">
                  <h3 className="font-medium mb-2">{selectedWorkflow.workflow.name}</h3>
                  <div className="flex items-center gap-4">
                    <Button
                      size="sm"
                      onClick={() => setShowInputDialog(true)}
                      disabled={!!activeExecution}
                      className="gap-2"
                    >
                      <Play className="h-3 w-3" />
                      Start Execution
                    </Button>
                    {activeExecution && (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => engine.pauseWorkflow(activeExecution.workflowId)}
                          disabled={activeExecution.status !== 'running'}
                        >
                          <Pause className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-destructive"
                          onClick={() => setActiveExecution(null)}
                        >
                          <Square className="h-3 w-3" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                {/* Progress */}
                {activeExecution && (
                  <div className="p-4 rounded-lg bg-card/50 border border-border/20">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Execution Progress</span>
                      <span className="text-sm text-muted-foreground">{executionProgress.toFixed(1)}%</span>
                    </div>
                    <Progress value={executionProgress} className="mb-2" />
                    <p className="text-xs text-muted-foreground">{executionMessage}</p>
                  </div>
                )}

                {/* Phases */}
                <div className="space-y-4">
                  {selectedWorkflow.execution_flow.phases.map((phase) => {
                    const status = getPhaseStatus(phase.name);
                    const phaseAgents = selectedWorkflow.agents.filter(a => 
                      phase.agents.includes(a.id)
                    );
                    
                    return (
                      <div key={phase.name} className="p-4 rounded-lg bg-card/50 border border-border/20">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <h4 className="font-medium capitalize">{phase.name}</h4>
                            <Badge variant={getPhaseBadgeVariant(status)}>
                              {status === 'running' && <Loader2 className="h-3 w-3 mr-1 animate-spin" />}
                              {status === 'completed' && <CheckCircle className="h-3 w-3 mr-1" />}
                              {status === 'error' && <XCircle className="h-3 w-3 mr-1" />}
                              {status === 'pending' && <Clock className="h-3 w-3 mr-1" />}
                              {status}
                            </Badge>
                            {phase.parallel && (
                              <Badge variant="outline" className="text-xs">
                                Parallel
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        {/* Phase Agents */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                          {phaseAgents.map((agent) => {
                            const status = agentStatus[agent.id] || 'idle';
                            return (
                              <div 
                                key={agent.id}
                                className={`p-3 rounded-md bg-background/50 border text-sm transition-all ${
                                  status === 'running' ? 'border-orange-500/40 bg-orange-500/5' :
                                  status === 'completed' ? 'border-green-500/40 bg-green-500/5' :
                                  status === 'error' ? 'border-destructive/40 bg-destructive/5' :
                                  'border-border/10'
                                }`}
                              >
                                <div className="flex items-center gap-2 mb-1">
                                  {getAgentIcon(agent.id)}
                                  <span className="font-medium">{agent.name}</span>
                                  {status === 'running' && (
                                    <Loader2 className="h-3 w-3 animate-spin text-orange-500" />
                                  )}
                                  {status === 'completed' && (
                                    <CheckCircle className="h-3 w-3 text-green-500" />
                                  )}
                                  {status === 'error' && (
                                    <XCircle className="h-3 w-3 text-destructive" />
                                  )}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {agent.dependencies.length > 0 && (
                                    <span>Depends on: {agent.dependencies.join(', ')}</span>
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <GitBranch className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Workflow Selected</h3>
                <p className="text-muted-foreground">Select a workflow from the Workflows tab to execute</p>
              </div>
            )}
          </TabsContent>

          {/* Log Tab */}
          <TabsContent value="log" className="space-y-4">
            <Card className="border-border/20">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Execution Log</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96 w-full rounded-md border p-4 bg-black/5">
                  <div className="space-y-1 font-mono text-xs">
                    {executionLog.length > 0 ? (
                      executionLog.map((log, index) => (
                        <div key={index} className="text-muted-foreground">
                          {log}
                        </div>
                      ))
                    ) : (
                      <div className="text-center text-muted-foreground">
                        No execution logs yet. Start a workflow to see logs here.
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Error Display */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mt-4 p-3 rounded-lg bg-destructive/10 border border-destructive/20 flex items-start gap-2 text-sm text-destructive"
            >
              <AlertCircle className="h-4 w-4 flex-shrink-0 mt-0.5" />
              <span>{error}</span>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>

      {/* Input Dialog */}
      <Dialog open={showInputDialog} onOpenChange={setShowInputDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Configure Workflow Inputs</DialogTitle>
            <DialogDescription>
              Provide the required inputs for the workflow execution
            </DialogDescription>
          </DialogHeader>
          
          <ScrollArea className="max-h-[50vh] pr-4">
            <div className="space-y-4 py-4">
              {Object.entries(workflowInputs).map(([key, value]) => (
                <div key={key} className="space-y-2">
                  <Label htmlFor={key}>{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</Label>
                  {key.includes('context') || key.includes('description') ? (
                    <Textarea
                      id={key}
                      value={value}
                      onChange={(e) => setWorkflowInputs({...workflowInputs, [key]: e.target.value})}
                      placeholder={`Enter ${key}`}
                      rows={3}
                    />
                  ) : (
                    <Input
                      id={key}
                      value={value}
                      onChange={(e) => setWorkflowInputs({...workflowInputs, [key]: e.target.value})}
                      placeholder={`Enter ${key}`}
                    />
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowInputDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => {
              setShowInputDialog(false);
              startWorkflow();
            }}>
              Start Workflow
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default ClaudeFlowWorkflow;