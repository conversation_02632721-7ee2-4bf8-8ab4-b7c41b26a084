{"id": "b058a058-5f51-4fef-bc24-1d3df1ab728a", "name": "swarm-optimization-centralized", "description": "Benchmark: Analyze swarm performance bottlenecks and optimization opportunities", "status": "completed", "config": {"name": "swarm-optimization-centralized", "description": "Benchmark: Analyze swarm performance bottlenecks and optimization opportunities", "strategy": "optimization", "mode": "centralized", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 300, "max_retries": 3, "parallel": false, "background": false, "monitoring": true, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "../reports/swarm-optimization", "verbose": false}, "tasks": [{"id": "fe76d182-63f8-4e26-942d-867d19bd91b2", "objective": "Analyze swarm performance bottlenecks and optimization opportunities", "description": "Benchmark task: Analyze swarm performance bottlenecks and optimization opportunities", "strategy": "optimization", "mode": "centralized", "parameters": {}, "timeout": 300, "max_retries": 3, "priority": 1, "status": "pending", "created_at": "2025-06-14T18:38:04.169194", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "ad747f0b-0b79-4fda-b11c-0cd3c2254013", "task_id": "fe76d182-63f8-4e26-942d-867d19bd91b2", "agent_id": "optimization-agent", "status": "success", "output": {"optimization_results": "Optimization completed for: Analyze swarm performance bottlenecks and optimization opportunities", "performance_gain": "25% improvement", "optimizations_applied": ["caching", "indexing", "compression"]}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.180581, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 30.0, "memory_mb": 320, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T18:38:04.349922", "started_at": "2025-06-14T18:38:04.169257", "completed_at": "2025-06-14T18:38:04.349870", "duration": 0.180613}], "metrics": {"total_tasks": 1, "completed_tasks": 1, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.180581, "total_execution_time": 0.180581, "success_rate": 1.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T18:38:04.169222", "started_at": "2025-06-14T18:38:04.169248", "completed_at": "2025-06-14T18:38:04.349953", "duration": 0.180705, "error_log": [], "metadata": {}}