# 📚 Claude Flow v2.0.0 Documentation

Welcome to the comprehensive documentation for Claude Flow v2.0.0 - Intelligent AI Agent Coordination Platform!

## 🚀 Getting Started

### Quick Links
- **[Quick Start Guide](./quick-start.md)** - Get up and running in minutes
- **[Claude Code Integration](./claude-code-setup.md)** - MCP configuration guide
- **[CLI Reference](./cli-reference.md)** - Complete command reference

## 👑 Hive Mind System (NEW!)

### Core Features
- **[Hive Mind Overview](./hive-mind/overview.md)** - Queen-led swarm intelligence
- **[Interactive Wizard](./hive-mind/wizard-guide.md)** - Easy setup walkthrough
- **[CLI Commands](./hive-mind/cli-commands.md)** - Complete command reference
- **[Examples](./hive-mind/examples.md)** - Real-world usage patterns

## 📖 Core Documentation

### Architecture & Configuration
- **[Architecture Overview](./02-architecture-overview.md)** - System design and components
- **[Neural Networks](./neural-networks.md)** - WASM neural processing
- **[Memory Systems](./memory-management.md)** - Persistent storage and collective memory
- **[Swarm Coordination](./swarm-coordination.md)** - Multi-agent orchestration

### Enterprise Features
- **[Workflow Automation](./workflow-automation.md)** - CI/CD pipeline setup
- **[GitHub Integration](./github-automation.md)** - Repository management
- **[WebUI Guide](./webui-guide.md)** - Browser interface
- **[Security Guide](./security-guide.md)** - Enterprise security setup

## 🛠️ Specialized Guides

### Setup & Initialization
- **[Batch Initialization](./batch-initialization.md)** - Batch project setup
- **[Initialization Scenarios](./initialization-scenarios.md)** - Common setup scenarios
- **[Initialization Troubleshooting](./initialization-troubleshooting.md)** - Solving setup issues
- **[Optimized Initialization](./optimized-initialization.md)** - Performance optimizations
- **[Template Customization](./template-customization.md)** - Customizing project templates

### Performance & Monitoring
- **[Performance Comparison](./performance-comparison.md)** - Benchmarks and optimizations
- **[Troubleshooting](./09-troubleshooting.md)** - Common issues and solutions

## 📊 Reports & Analysis

### Performance Reports
- **[Benchmark Analysis](./reports/COMPREHENSIVE_BENCHMARK_ANALYSIS_REPORT.md)** - Performance benchmarks
- **[Hive Mind Performance](./reports/hive-mind-performance-analysis.md)** - Swarm optimization results
- **[TypeScript Validation](./reports/typescript-validation-report.md)** - Code quality metrics

### Development Documentation  
- **[Directory Reorganization](./development/DIRECTORY_REORGANIZATION_SUMMARY.md)** - Project structure improvements
- **[Test Consolidation](./development/TEST_CONSOLIDATION_SUMMARY.md)** - Testing strategy
- **[Cleanup Summary](./development/CLEANUP_SUMMARY.md)** - Code maintenance

### Strategy Analysis
- **[Hive Mind Optimization](./analysis/HIVE_MIND_OPTIMIZATION_STRATEGY.md)** - System optimization strategies

## 🎮 User Guides

### Essential Guides
- **[Coordination Guide](./guides/coordination.md)** - Multi-agent coordination
- **[Memory Bank Guide](./guides/memory-bank.md)** - Using the memory system
- **[Optimized Init Usage](./optimized-init-usage-guide.md)** - Efficient project setup

### UI & Interfaces
- **[Swarm Blessed UI](./swarm-blessed-ui.md)** - Terminal UI interface
- **[Start Command Consolidation](./start-command-consolidation.md)** - Command management

## 🔧 Development & API

### API Documentation
- **[API Reference](./api/)** - Complete API documentation
- **[MCP Implementation](./mcp-implementation.md)** - MCP server implementation

### Examples & Demos
- **[Examples](./examples/)** - Code examples and demos
- **[REPL Demo](./repl-demo.md)** - Interactive REPL usage

## 📝 Version History

### v2.0.0 Highlights
- **Hive Mind System**: Queen-led swarm intelligence with collective memory
- **87 MCP Tools**: Complete integration with Claude Code
- **Neural Networks**: Real WASM neural processing with training
- **Performance**: 2.8-4.4x speed improvements, 32.3% token reduction
- **Enterprise Ready**: Security, monitoring, and production features

## 🤝 Contributing

Want to improve the documentation? See our [Contributing Guidelines](../CONTRIBUTING.md) for how to help!

---

## 🔍 Quick Navigation

| Category | Description | Key Files |
|----------|-------------|-----------|
| **Getting Started** | Setup and basic usage | `quick-start.md`, `01-getting-started.md` |
| **Core Features** | Main functionality | `04-agent-management.md`, `05-task-coordination.md` |
| **Advanced** | Power user features | `10-advanced-usage.md`, `07-mcp-integration.md` |
| **Troubleshooting** | Problem solving | `09-troubleshooting.md`, `initialization-troubleshooting.md` |
| **API** | Developer resources | `api/`, `cli-reference.md` |

---

**Built with ❤️ by the Claude-Flow team | Powered by Claude AI**