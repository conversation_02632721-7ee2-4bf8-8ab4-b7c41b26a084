{"id": "bdb0f91c-4de0-4459-aacd-702df5b8dd3d", "name": "demo-analysis-mesh", "description": "Demo: Analysis Strategy - Mesh", "status": "completed", "config": {"name": "demo-analysis-mesh", "description": "Demo: Analysis Strategy - Mesh", "strategy": "analysis", "mode": "mesh", "max_agents": 4, "max_tasks": 100, "timeout": 3600, "task_timeout": 300, "max_retries": 3, "parallel": false, "background": false, "monitoring": true, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./demo_reports", "verbose": true}, "tasks": [{"id": "b101c772-d7d5-4536-9b97-4225157ede35", "objective": "Analyze user behavior patterns and generate insights", "description": "Benchmark task: Analyze user behavior patterns and generate insights", "strategy": "analysis", "mode": "mesh", "parameters": {}, "timeout": 300, "max_retries": 3, "priority": 1, "status": "pending", "created_at": "2025-06-14T16:40:21.619635", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "56f4875c-e7aa-43ba-b621-036ed4882ddb", "task_id": "b101c772-d7d5-4536-9b97-4225157ede35", "agent_id": "analysis-agent", "status": "success", "output": {"analysis_results": "Analysis completed for: Analyze user behavior patterns and generate insights", "insights": ["trend 1", "pattern 2", "correlation 3"], "methodology": "statistical analysis"}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.150271, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 20.0, "memory_mb": 192, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T16:40:21.770001", "started_at": "2025-06-14T16:40:21.619671", "completed_at": "2025-06-14T16:40:21.769965", "duration": 0.150294}], "metrics": {"total_tasks": 1, "completed_tasks": 1, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.150271, "total_execution_time": 0.150271, "success_rate": 1.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T16:40:21.619655", "started_at": "2025-06-14T16:40:21.619659", "completed_at": "2025-06-14T16:40:21.770023", "duration": 0.150364, "error_log": [], "metadata": {}}