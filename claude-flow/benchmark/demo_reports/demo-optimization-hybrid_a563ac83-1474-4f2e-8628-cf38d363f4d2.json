{"id": "a563ac83-1474-4f2e-8628-cf38d363f4d2", "name": "demo-optimization-hybrid", "description": "Demo: Optimization Strategy - Hybrid", "status": "completed", "config": {"name": "demo-optimization-hybrid", "description": "Demo: Optimization Strategy - Hybrid", "strategy": "optimization", "mode": "hybrid", "max_agents": 7, "max_tasks": 100, "timeout": 3600, "task_timeout": 300, "max_retries": 3, "parallel": false, "background": false, "monitoring": true, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./demo_reports", "verbose": true}, "tasks": [{"id": "1c730c1a-58e6-4618-949f-cadd9d9b6d5f", "objective": "Optimize database queries and improve application performance", "description": "Benchmark task: Optimize database queries and improve application performance", "strategy": "optimization", "mode": "hybrid", "parameters": {}, "timeout": 300, "max_retries": 3, "priority": 1, "status": "pending", "created_at": "2025-06-17T16:57:16.908468", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "8e0d6113-1535-40ee-9c26-c2db5ee76453", "task_id": "1c730c1a-58e6-4618-949f-cadd9d9b6d5f", "agent_id": "optimization-agent", "status": "success", "output": {"optimization_results": "Optimization completed for: Optimize database queries and improve application performance", "performance_gain": "25% improvement", "optimizations_applied": ["caching", "indexing", "compression"]}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.180309, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 30.0, "memory_mb": 320, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-17T16:57:17.088883", "started_at": "2025-06-17T16:57:16.908518", "completed_at": "2025-06-17T16:57:17.088849", "duration": 0.180331}], "metrics": {"total_tasks": 1, "completed_tasks": 1, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.180309, "total_execution_time": 0.180309, "success_rate": 1.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-17T16:57:16.908497", "started_at": "2025-06-17T16:57:16.908502", "completed_at": "2025-06-17T16:57:17.088903", "duration": 0.180401, "error_log": [], "metadata": {}}