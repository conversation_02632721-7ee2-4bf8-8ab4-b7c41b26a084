# Git and version control
.git
.gitignore
.gitattributes

# Documentation
*.md
README*
CHANGELOG*
LICENSE*
docs/

# Development files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs and temporary files
*.log
logs/
tmp/
temp/

# Test outputs and results
benchmark-results/
automation-results/
hive-benchmarks/
test-output/
reports/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
venv/
env/
ENV/

# IDEs and editors
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
Dockerfile*
docker-compose*
.dockerignore