# Core benchmark dependencies
psutil>=5.9.0
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.5.0
seaborn>=0.11.0
jinja2>=3.0.0

# Testing and automation
pytest>=7.0.0
pytest-xdist>=2.5.0
pytest-timeout>=2.1.0
pytest-html>=3.1.0
pytest-cov>=4.0.0

# Performance monitoring
memory-profiler>=0.60.0
line-profiler>=4.0.0
py-spy>=0.3.0

# Data processing and analysis
scipy>=1.7.0
scikit-learn>=1.0.0
plotly>=5.10.0

# Report generation
reportlab>=3.6.0
weasyprint>=56.0

# Utilities
click>=8.0.0
rich>=12.0.0
tqdm>=4.64.0
pyyaml>=6.0
toml>=0.10.0
requests>=2.28.0

# Development and testing
black>=22.0.0
flake8>=5.0.0
mypy>=0.991
bandit>=1.7.0
safety>=2.0.0