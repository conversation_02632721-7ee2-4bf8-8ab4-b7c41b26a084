{"id": "595fb042-57aa-4126-a3d8-d5a3099f6922", "name": "benchmark-analysis-centralized", "description": "Benchmark: Analyze data trends", "status": "completed", "config": {"name": "benchmark-analysis-centralized", "description": "Benchmark: Analyze data trends", "strategy": "analysis", "mode": "centralized", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 300, "max_retries": 3, "parallel": false, "background": false, "monitoring": false, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./reports", "verbose": false}, "tasks": [{"id": "7be8d9dd-bf5e-4330-a451-3f89943c4c84", "objective": "Analyze data trends", "description": "Benchmark task: Analyze data trends", "strategy": "analysis", "mode": "centralized", "parameters": {}, "timeout": 300, "max_retries": 3, "priority": 1, "status": "pending", "created_at": "2025-06-14T16:37:11.341042", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "96d44526-e9f3-48d7-b0ce-a7b080318c50", "task_id": "7be8d9dd-bf5e-4330-a451-3f89943c4c84", "agent_id": "analysis-agent", "status": "success", "output": {"analysis_results": "Analysis completed for: Analyze data trends", "insights": ["trend 1", "pattern 2", "correlation 3"], "methodology": "statistical analysis"}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.15108, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 20.0, "memory_mb": 192, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-14T16:37:11.492266", "started_at": "2025-06-14T16:37:11.341126", "completed_at": "2025-06-14T16:37:11.492230", "duration": 0.151104}], "metrics": {"total_tasks": 1, "completed_tasks": 1, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.15108, "total_execution_time": 0.15108, "success_rate": 1.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-14T16:37:11.341088", "started_at": "2025-06-14T16:37:11.341117", "completed_at": "2025-06-14T16:37:11.492290", "duration": 0.151173, "error_log": [], "metadata": {}}