{"id": "88d86f43-6c07-4615-b998-50f4fa55c20e", "name": "comparison-development-hierarchical", "description": "Performance comparison benchmark", "status": "completed", "config": {"name": "comparison-development-hierarchical", "description": "Performance comparison benchmark", "strategy": "development", "mode": "hierarchical", "max_agents": 5, "max_tasks": 100, "timeout": 3600, "task_timeout": 30, "max_retries": 2, "parallel": false, "background": false, "monitoring": true, "quality_threshold": 0.8, "resource_limits": {"max_memory_mb": 1024, "max_cpu_percent": 80, "max_network_mbps": 100}, "output_formats": ["json"], "output_directory": "./reports", "verbose": false}, "tasks": [{"id": "ba5f6afc-69b7-411d-abbe-d739ae6c00fb", "objective": "Build a REST API for user management", "description": "Benchmark task: Build a REST API for user management", "strategy": "development", "mode": "hierarchical", "parameters": {}, "timeout": 30, "max_retries": 2, "priority": 1, "status": "pending", "created_at": "2025-06-17T16:57:30.745315", "started_at": null, "completed_at": null, "duration": null, "assigned_agents": [], "parent_task_id": null, "subtasks": [], "dependencies": []}], "results": [{"id": "0d413293-116e-4ed9-8fcf-8588b838a600", "task_id": "ba5f6afc-69b7-411d-abbe-d739ae6c00fb", "agent_id": "development-agent", "status": "success", "output": {"code_implementation": "Code implementation completed for: Build a REST API for user management", "files_created": ["main.py", "utils.py", "tests.py"], "lines_of_code": 250, "test_coverage": 0.95, "code_quality": 0.9}, "errors": [], "warnings": [], "performance_metrics": {"execution_time": 0.200329, "queue_time": 0.0, "throughput": 0.0, "success_rate": 1.0, "error_rate": 0.0, "retry_count": 0, "coordination_overhead": 0.0, "communication_latency": 0.0}, "quality_metrics": {"accuracy_score": 0.0, "completeness_score": 0.0, "consistency_score": 0.0, "relevance_score": 0.0, "overall_quality": 0.0, "review_score": null, "automated_score": null}, "resource_usage": {"cpu_percent": 25.0, "memory_mb": 256, "network_bytes_sent": 0, "network_bytes_recv": 0, "disk_bytes_read": 0, "disk_bytes_write": 0, "peak_memory_mb": 0.0, "average_cpu_percent": 0.0}, "execution_details": {}, "created_at": "2025-06-17T16:57:30.945712", "started_at": "2025-06-17T16:57:30.745325", "completed_at": "2025-06-17T16:57:30.945676", "duration": 0.200351}], "metrics": {"total_tasks": 1, "completed_tasks": 0, "failed_tasks": 0, "total_agents": 0, "active_agents": 0, "average_execution_time": 0.200329, "total_execution_time": 0.200329, "success_rate": 0.0, "throughput": 0.0, "resource_efficiency": 0.0, "coordination_efficiency": 0.0, "quality_score": 0.0, "peak_memory_usage": 0.0, "total_cpu_time": 0.0, "network_overhead": 0.0}, "created_at": "2025-06-17T16:57:30.745296", "started_at": "2025-06-17T16:57:30.745300", "completed_at": "2025-06-17T16:57:30.945735", "duration": 0.200435, "error_log": [], "metadata": {}}