{"errorHandling": {"global": {"enabled": true, "debug": false, "correlationTracking": true, "performanceMonitoring": true}, "retry": {"maxRetries": 3, "initialDelay": 1000, "maxDelay": 30000, "backoffMultiplier": 2, "jitterMax": 0.1, "retryableErrors": ["ECONNREFUSED", "ECONNRESET", "ETIMEDOUT", "ENOTFOUND", "SQLITE_BUSY", "SQLITE_LOCKED", "timeout", "network", "temporary"]}, "circuitBreaker": {"failureThreshold": 5, "recoveryTimeout": 30000, "monitoringPeriod": 60000, "components": {"persistence": {"failureThreshold": 10, "recoveryTimeout": 60000}, "wasm": {"failureThreshold": 3, "recoveryTimeout": 120000}, "mcp": {"failureThreshold": 5, "recoveryTimeout": 30000}, "neural": {"failureThreshold": 8, "recoveryTimeout": 45000}}}, "healthMonitoring": {"enabled": true, "checkInterval": 60000, "monitoringWindow": 300000, "alertThresholds": {"errorRate": 0.1, "criticalErrors": 5, "circuitBreakerOpenings": 3, "memoryUsage": 0.8, "responseTime": 10000}}, "logging": {"enabled": true, "level": "info", "maxLogSize": 1000, "structuredLogging": true, "logRotation": {"enabled": true, "maxFiles": 5, "maxSize": "10MB"}, "destinations": {"console": {"enabled": true, "colorize": true, "timestamp": true}, "file": {"enabled": true, "filename": "logs/error-handling.log", "level": "error"}, "metrics": {"enabled": true, "filename": "logs/metrics.log", "level": "info"}}}}, "componentSpecific": {"persistence": {"errorHandling": {"enabled": true, "fallbackMode": "memory", "transactionSafety": true, "backupOnRiskyOperations": true, "dataValidation": true, "corruptionDetection": true, "memoryCleanup": {"enabled": true, "interval": 300000, "ttlCleanup": true}, "backup": {"enabled": true, "path": "./data/backups", "retentionDays": 7, "compressionEnabled": false}}, "timeouts": {"connection": 10000, "query": 30000, "transaction": 60000}, "validation": {"swarmData": {"required": ["id", "name", "topology", "maxAgents"], "constraints": {"id": {"type": "string", "minLength": 1, "maxLength": 100}, "name": {"type": "string", "minLength": 1, "maxLength": 255}, "topology": {"enum": ["mesh", "hierarchical", "ring", "star", "centralized", "distributed"]}, "maxAgents": {"type": "number", "min": 1, "max": 1000}}}, "agentData": {"required": ["id", "swarmId", "name", "type"], "constraints": {"id": {"type": "string", "minLength": 1, "maxLength": 100}, "swarmId": {"type": "string", "minLength": 1, "maxLength": 100}, "name": {"type": "string", "minLength": 1, "maxLength": 255}, "type": {"enum": ["researcher", "coder", "analyst", "optimizer", "coordinator", "tester", "architect"]}}}}}, "wasm": {"errorHandling": {"enabled": true, "fallbacksEnabled": true, "validationEnabled": true, "integrityChecks": true, "gracefulDegradation": true}, "timeouts": {"loading": 30000, "initialization": 15000, "validation": 5000}, "fallbacks": {"core": {"enabled": true, "implementation": "javascript", "performance": "degraded"}, "neural": {"enabled": true, "implementation": "javascript", "performance": "limited"}, "forecasting": {"enabled": true, "implementation": "simple", "performance": "basic"}}, "capabilities": {"simdRequired": false, "threadsRequired": false, "memory64Required": false}}, "mcp": {"errorHandling": {"enabled": true, "parameterValidation": true, "prerequisiteChecking": true, "healthChecking": true, "performanceMonitoring": true}, "timeouts": {"swarm_init": 30000, "agent_spawn": 15000, "task_orchestrate": 20000, "neural_train": 60000, "benchmark_run": 120000, "memory_usage": 5000, "swarm_status": 5000, "task_status": 5000, "task_results": 10000}, "validation": {"cacheEnabled": true, "cacheSize": 1000, "strictMode": true}, "systemChecks": {"memoryThreshold": 2048, "cpuLoadThreshold": 2.0, "agentAvailabilityCheck": true, "neuralCapabilityCheck": true}}, "neural": {"errorHandling": {"enabled": true, "memoryOptimization": true, "trainingValidation": true, "performanceTracking": true}, "timeouts": {"training": 300000, "inference": 10000, "validation": 5000}, "memoryManagement": {"pooling": true, "garbageCollection": true, "compressionEnabled": true, "maxMemoryUsage": 1024}, "validation": {"trainingData": true, "networkArchitecture": true, "hyperparameters": true}}}, "alerting": {"enabled": true, "channels": {"console": {"enabled": true, "levels": ["critical", "high"], "format": "detailed"}, "email": {"enabled": false, "smtp": {"host": "localhost", "port": 587, "secure": false}, "recipients": ["<EMAIL>"], "levels": ["critical"]}, "webhook": {"enabled": false, "url": "https://hooks.example.com/alerts", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer token"}, "levels": ["critical", "high"]}, "slack": {"enabled": false, "webhookUrl": "https://hooks.slack.com/services/...", "channel": "#alerts", "levels": ["critical", "high"], "mentionOnCritical": true}}, "rules": {"criticalErrors": {"condition": "severity == 'critical'", "cooldown": 300000, "action": "immediate"}, "highErrorRate": {"condition": "errorRate > 0.1 over 5m", "cooldown": 600000, "action": "escalate"}, "circuitBreakerOpen": {"condition": "circuitBreaker.state == 'OPEN'", "cooldown": 180000, "action": "notify"}, "memoryPressure": {"condition": "memoryUsage > 0.8", "cooldown": 300000, "action": "warn"}, "slowOperations": {"condition": "avgResponseTime > 10000", "cooldown": 600000, "action": "investigate"}}, "escalation": {"enabled": true, "levels": [{"name": "L1", "threshold": 1, "timeout": 300000, "contacts": ["<EMAIL>"]}, {"name": "L2", "threshold": 3, "timeout": 600000, "contacts": ["<EMAIL>"]}, {"name": "L3", "threshold": 5, "timeout": 900000, "contacts": ["<EMAIL>"]}]}}, "metrics": {"enabled": true, "collection": {"interval": 30000, "retention": 86400000, "aggregation": "average"}, "exports": {"prometheus": {"enabled": false, "port": 9090, "path": "/metrics", "labels": {"service": "ruv-swarm", "environment": "production"}}, "json": {"enabled": true, "file": "metrics/error-metrics.json", "interval": 60000}, "csv": {"enabled": false, "file": "metrics/error-metrics.csv", "interval": 300000}}, "tracked": {"errorRate": {"enabled": true, "window": 300000, "aggregation": "rate"}, "responseTime": {"enabled": true, "percentiles": [50, 90, 95, 99], "aggregation": "histogram"}, "memoryUsage": {"enabled": true, "aggregation": "gauge"}, "circuitBreakerState": {"enabled": true, "aggregation": "state"}, "operationCounts": {"enabled": true, "breakdown": ["component", "operation", "status"], "aggregation": "counter"}}}, "recovery": {"strategies": {"retry": {"applicable": ["network", "timeout", "temporary"], "maxAttempts": 3, "backoff": "exponential"}, "fallback": {"applicable": ["wasm", "neural", "mcp"], "degradationAcceptable": true}, "circuitBreaker": {"applicable": ["persistence", "external"], "autoRecovery": true}, "gracefulDegradation": {"applicable": ["optional-features"], "userNotification": true}, "restart": {"applicable": ["memory-leak", "corruption"], "automated": false, "requiresApproval": true}, "manualIntervention": {"applicable": ["critical", "security"], "escalationRequired": true}}, "automation": {"enabled": true, "safetyChecks": true, "rollbackCapability": true, "approvalRequired": {"restart": true, "dataRecovery": true, "configurationChanges": true}}}, "testing": {"errorInjection": {"enabled": false, "probability": 0.01, "components": ["persistence", "wasm", "mcp"], "scenarios": ["timeout", "validation", "corruption"]}, "chaos": {"enabled": false, "intensity": "low", "schedule": "manual"}}, "maintenance": {"cleanup": {"enabled": true, "schedule": "0 2 * * *", "tasks": ["expiredMemoryCleanup", "logRotation", "metricArchival", "<PERSON><PERSON><PERSON><PERSON>"]}, "healthChecks": {"enabled": true, "schedule": "*/5 * * * *", "comprehensive": false}}, "environments": {"development": {"errorHandling": {"debug": true, "retry": {"maxRetries": 1, "initialDelay": 500}}, "alerting": {"enabled": false}, "testing": {"errorInjection": {"enabled": true, "probability": 0.05}}}, "staging": {"errorHandling": {"debug": false, "retry": {"maxRetries": 2}}, "alerting": {"enabled": true, "channels": {"console": {"enabled": true}, "webhook": {"enabled": true}}}}, "production": {"errorHandling": {"debug": false, "performanceMonitoring": true}, "alerting": {"enabled": true, "channels": {"console": {"enabled": true}, "email": {"enabled": true}, "slack": {"enabled": true}}, "escalation": {"enabled": true}}, "metrics": {"exports": {"prometheus": {"enabled": true}}}}}}