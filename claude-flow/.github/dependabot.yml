version: 2
updates:
  # Enable version updates for npm dependencies in the memory module
  - package-ecosystem: "npm"
    directory: "/memory"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    reviewers:
      - "ruvnet"
    labels:
      - "dependencies"
      - "npm"
    commit-message:
      prefix: "chore"
      include: "scope"

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "ruvnet"
    labels:
      - "dependencies"
      - "github-actions"
    commit-message:
      prefix: "chore"
      include: "scope"

  # Enable version updates for Deno dependencies
  - package-ecosystem: "gomod"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "ruvnet"
    labels:
      - "dependencies"
      - "deno"
    commit-message:
      prefix: "chore"
      include: "scope"