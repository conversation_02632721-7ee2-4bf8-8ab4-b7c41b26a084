name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  DENO_VERSION: '1.40.0'
  NODE_VERSION: '18'

jobs:
  # Code quality and security checks
  security:
    name: Security & Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: ${{ env.DENO_VERSION }}

      - name: Run security audit
        run: |
          # Check for security vulnerabilities in dependencies
          deno cache --reload src/cli/index.ts
          
      - name: Lint code
        run: |
          deno lint

      - name: Format check
        run: |
          deno fmt --check

      - name: Type check
        run: |
          deno check src/cli/index.ts

  # Unit and integration tests
  test:
    name: Test Suite
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        test-type: [unit, integration, e2e]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: ${{ env.DENO_VERSION }}

      - name: Setup Node.js (for some tests)
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache Deno dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/deno
          key: ${{ runner.os }}-deno-${{ hashFiles('**/deno.lock') }}
          restore-keys: |
            ${{ runner.os }}-deno-

      - name: Install dependencies
        run: |
          deno cache --reload src/cli/index.ts

      - name: Create test directories
        run: |
          mkdir -p test-results/coverage
          mkdir -p test-results/reports

      - name: Run unit tests
        if: matrix.test-type == 'unit'
        run: |
          deno task test:unit --coverage=test-results/coverage

      - name: Run integration tests  
        if: matrix.test-type == 'integration'
        run: |
          deno task test:integration --coverage=test-results/coverage

      - name: Run end-to-end tests
        if: matrix.test-type == 'e2e'
        run: |
          deno task test:e2e --coverage=test-results/coverage

      - name: Generate coverage report
        if: matrix.os == 'ubuntu-latest'
        run: |
          deno coverage test-results/coverage --lcov --output=test-results/coverage.lcov
          deno coverage test-results/coverage --html --output=test-results/coverage-html

      - name: Upload coverage to Codecov
        if: matrix.os == 'ubuntu-latest' && matrix.test-type == 'unit'
        uses: codecov/codecov-action@v4
        with:
          file: test-results/coverage.lcov
          flags: ${{ matrix.test-type }}
          name: codecov-${{ matrix.os }}-${{ matrix.test-type }}
          token: ${{ secrets.CODECOV_TOKEN }}

      - name: Archive test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.os }}-${{ matrix.test-type }}
          path: test-results/
          retention-days: 30

  # Performance and load testing
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [test]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: ${{ env.DENO_VERSION }}

      - name: Run performance tests
        run: |
          deno task test:performance --json --output=performance-results.json

      - name: Archive performance results
        uses: actions/upload-artifact@v4
        with:
          name: performance-results
          path: performance-results.json

      - name: Performance regression check
        run: |
          # Compare with baseline performance metrics
          deno run --allow-read scripts/check-performance-regression.ts

  # Build and package
  build:
    name: Build & Package
    runs-on: ${{ matrix.os }}
    needs: [security, test]
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: ${{ env.DENO_VERSION }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Build CLI binary
        run: |
          deno compile --allow-all --output=dist/claude-flow-${{ runner.os }} src/cli/index.ts

      - name: Test binary
        run: |
          ./dist/claude-flow-${{ runner.os }} --version

      - name: Build NPM package
        if: matrix.os == 'ubuntu-latest'
        run: |
          npm run build

      - name: Archive build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ matrix.os }}
          path: dist/
          retention-days: 90

  # Integration testing with real Claude API
  claude-integration:
    name: Claude API Integration
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: ${{ env.DENO_VERSION }}

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-Linux

      - name: Run Claude integration tests
        env:
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        run: |
          chmod +x dist/claude-flow-Linux
          deno task test:claude-integration
        continue-on-error: true

  # Documentation and examples validation
  docs:
    name: Documentation & Examples
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: ${{ env.DENO_VERSION }}

      - name: Validate example configurations
        run: |
          deno run --allow-read scripts/validate-examples.ts

      - name: Test documentation code samples
        run: |
          deno task test:docs

      - name: Check for broken links
        run: |
          deno run --allow-net --allow-read scripts/check-links.ts

  # Deployment and release
  deploy:
    name: Deploy & Release
    runs-on: ubuntu-latest
    needs: [security, test, performance, build, claude-integration, docs]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: 'https://registry.npmjs.org'

      - name: Download all artifacts
        uses: actions/download-artifact@v4

      - name: Prepare release
        run: |
          # Semantic versioning based on commit messages
          npm install -g semantic-release @semantic-release/git @semantic-release/github
          
      - name: Semantic Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: |
          semantic-release

      - name: Deploy to Deno Land
        if: steps.semantic-release.outputs.new_release_published == 'true'
        run: |
          # Submit to deno.land/x registry
          curl -X POST "https://api.deno.land/webhook/gh/claude-flow" \
            -H "Authorization: Bearer ${{ secrets.DENO_WEBHOOK_SECRET }}"

  # Monitoring and alerts
  monitoring:
    name: Setup Monitoring
    runs-on: ubuntu-latest
    needs: [deploy]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
      - name: Setup monitoring
        run: |
          echo "Setting up monitoring dashboards and alerts"
          # This would integrate with monitoring services

      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#claude-flow-deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        if: always()

# Workflow notifications
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [security, test, performance, build, claude-integration, docs]
    if: always()
    
    steps:
      - name: Workflow Summary
        run: |
          echo "## Workflow Summary" >> $GITHUB_STEP_SUMMARY
          echo "- Security: ${{ needs.security.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Tests: ${{ needs.test.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Performance: ${{ needs.performance.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Build: ${{ needs.build.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Claude Integration: ${{ needs.claude-integration.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Documentation: ${{ needs.docs.result }}" >> $GITHUB_STEP_SUMMARY
