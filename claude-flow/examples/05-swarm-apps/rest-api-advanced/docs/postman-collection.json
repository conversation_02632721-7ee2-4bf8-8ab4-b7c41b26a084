{"info": {"name": "Advanced REST API", "description": "Postman collection for the Advanced REST API with authentication, products, and orders management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "refreshToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "productId", "value": "", "type": "string"}, {"key": "orderId", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has auth tokens\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('token');", "    pm.expect(jsonData).to.have.property('refreshToken');", "    pm.expect(jsonData).to.have.property('user');", "    ", "    // Save tokens for other requests", "    pm.collectionVariables.set(\"authToken\", jsonData.token);", "    pm.collectionVariables.set(\"refreshToken\", jsonData.refreshToken);", "    pm.collectionVariables.set(\"userId\", jsonData.user.id);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["// Generate random email for testing", "pm.variables.set(\"randomEmail\", \"user\" + Date.now() + \"@example.com\");"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{randomEmail}}\",\n    \"password\": \"password123\",\n    \"name\": \"Test User\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has auth tokens\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('token');", "    pm.expect(jsonData).to.have.property('refreshToken');", "    ", "    // Save tokens for other requests", "    pm.collectionVariables.set(\"authToken\", jsonData.token);", "    pm.collectionVariables.set(\"refreshToken\", jsonData.refreshToken);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "Get Current User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has user data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('user');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/me", "host": ["{{baseUrl}}"], "path": ["auth", "me"]}}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has new tokens\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('token');", "    pm.expect(jsonData).to.have.property('refreshToken');", "    ", "    // Update tokens", "    pm.collectionVariables.set(\"authToken\", jsonData.token);", "    pm.collectionVariables.set(\"refreshToken\", jsonData.refreshToken);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/refresh", "host": ["{{baseUrl}}"], "path": ["auth", "refresh"]}}}, {"name": "Logout", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/forgot-password", "host": ["{{baseUrl}}"], "path": ["auth", "forgot-password"]}}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"token\": \"reset-token-here\",\n    \"password\": \"newpassword123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["auth", "reset-password"]}}}, {"name": "Check Password Strength", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"password\": \"MyStr0ng!Pass\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/check-password", "host": ["{{baseUrl}}"], "path": ["auth", "check-password"]}}}]}, {"name": "Products", "item": [{"name": "Get All Products", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has products\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData).to.have.property('meta');", "    ", "    // Save first product ID if available", "    if (jsonData.data.length > 0) {", "        pm.collectionVariables.set(\"productId\", jsonData.data[0]._id);", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products?page=1&limit=20&sort=-createdAt", "host": ["{{baseUrl}}"], "path": ["products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "sort", "value": "-createdAt"}, {"key": "category", "value": "Electronics", "disabled": true}, {"key": "minPrice", "value": "10", "disabled": true}, {"key": "maxPrice", "value": "100", "disabled": true}, {"key": "search", "value": "laptop", "disabled": true}]}}}, {"name": "Get Product by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has product data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('_id');", "    pm.expect(jsonData.data).to.have.property('name');", "    pm.expect(jsonData.data).to.have.property('price');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"]}}}, {"name": "Create Product (Admin)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Product created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('_id');", "    ", "    // Save new product ID", "    pm.collectionVariables.set(\"productId\", jsonData.data._id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Product\",\n    \"description\": \"This is a test product\",\n    \"price\": 99.99,\n    \"category\": \"Electronics\",\n    \"stock\": 100,\n    \"sku\": \"TEST-SKU-001\",\n    \"tags\": [\"new\", \"featured\"],\n    \"specifications\": {\n        \"brand\": \"TestBrand\",\n        \"model\": \"TM-001\",\n        \"weight\": \"500g\"\n    }\n}"}, "url": {"raw": "{{baseUrl}}/products", "host": ["{{baseUrl}}"], "path": ["products"]}}}, {"name": "Update Product (Admin)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Product updated successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data.price).to.equal(149.99);", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Test Product\",\n    \"price\": 149.99,\n    \"stock\": 50\n}"}, "url": {"raw": "{{baseUrl}}/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"]}}}, {"name": "Delete Product (Admin)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"]}}}, {"name": "Get Products by Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/category/Electronics", "host": ["{{baseUrl}}"], "path": ["products", "category", "Electronics"]}}}, {"name": "Get Featured Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/featured", "host": ["{{baseUrl}}"], "path": ["products", "featured"]}}}, {"name": "Get Popular Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/popular", "host": ["{{baseUrl}}"], "path": ["products", "popular"]}}}, {"name": "Add Product Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"rating\": 5,\n    \"title\": \"Excellent product!\",\n    \"comment\": \"I love this product. Great quality and fast shipping.\"\n}"}, "url": {"raw": "{{baseUrl}}/products/{{productId}}/reviews", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}", "reviews"]}}}, {"name": "Update Inventory (Admin)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"stock\": 150,\n    \"operation\": \"set\"\n}"}, "url": {"raw": "{{baseUrl}}/products/{{productId}}/inventory", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}", "inventory"]}}}, {"name": "Get Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/categories/list", "host": ["{{baseUrl}}"], "path": ["products", "categories", "list"]}}}]}, {"name": "Orders", "item": [{"name": "Create Order", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Order created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('_id');", "    pm.expect(jsonData.data).to.have.property('orderNumber');", "    ", "    // Save order ID", "    pm.collectionVariables.set(\"orderId\", jsonData.data._id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"items\": [\n        {\n            \"product\": \"{{productId}}\",\n            \"quantity\": 2\n        }\n    ],\n    \"shippingAddress\": {\n        \"name\": \"John Doe\",\n        \"street\": \"123 Main St\",\n        \"city\": \"New York\",\n        \"state\": \"NY\",\n        \"zipCode\": \"10001\",\n        \"country\": \"USA\"\n    },\n    \"paymentMethod\": \"credit_card\",\n    \"notes\": \"Please deliver between 9 AM - 5 PM\"\n}"}, "url": {"raw": "{{baseUrl}}/orders", "host": ["{{baseUrl}}"], "path": ["orders"]}}}, {"name": "Get User Orders", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has orders\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData).to.have.property('meta');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders?page=1&limit=20&status=pending", "host": ["{{baseUrl}}"], "path": ["orders"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "status", "value": "pending"}]}}}, {"name": "Get Order by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has order data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('_id');", "    pm.expect(jsonData.data).to.have.property('orderNumber');", "    pm.expect(jsonData.data).to.have.property('totalAmount');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/{{orderId}}", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}"]}}}, {"name": "Update Order Status (Admin)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Order status updated\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.status).to.equal('processing');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"processing\"\n}"}, "url": {"raw": "{{baseUrl}}/orders/{{orderId}}/status", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}", "status"]}}}, {"name": "Cancel Order", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/orders/{{orderId}}", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}"]}}}, {"name": "Get All Orders (Admin)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/admin/all?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["orders", "admin", "all"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "status", "value": "pending", "disabled": true}, {"key": "startDate", "value": "2024-01-01", "disabled": true}, {"key": "endDate", "value": "2024-12-31", "disabled": true}]}}}, {"name": "Add Tracking (Admin)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"carrier\": \"FedEx\",\n    \"trackingNumber\": \"1234567890\",\n    \"estimatedDelivery\": \"2024-01-20\"\n}"}, "url": {"raw": "{{baseUrl}}/orders/{{orderId}}/tracking", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}", "tracking"]}}}, {"name": "Process Refund (Admin)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 50.00,\n    \"reason\": \"Customer requested partial refund\"\n}"}, "url": {"raw": "{{baseUrl}}/orders/{{orderId}}/refund", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}", "refund"]}}}, {"name": "Get Order Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/statistics/summary?period=month", "host": ["{{baseUrl}}"], "path": ["orders", "statistics", "summary"], "query": [{"key": "period", "value": "month"}]}}}, {"name": "Get Sales Report (Admin)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/reports/sales?startDate=2024-01-01&endDate=2024-12-31", "host": ["{{baseUrl}}"], "path": ["orders", "reports", "sales"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-12-31"}]}}}, {"name": "Get Order Invoice", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/{{orderId}}/invoice", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}", "invoice"]}}}]}, {"name": "Health", "item": [{"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"API is healthy\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.equal('healthy');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "Readiness Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health/ready", "host": ["{{baseUrl}}"], "path": ["health", "ready"]}}}, {"name": "Liveness Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health/live", "host": ["{{baseUrl}}"], "path": ["health", "live"]}}}]}]}