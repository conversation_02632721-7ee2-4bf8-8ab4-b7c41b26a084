# Nginx configuration for claude-code-flow load balancing
events {
    worker_connections 1024;
}

http {
    upstream claude_flow_backend {
        server claude-flow-dev:3000;
        server claude-flow-prod:3000 backup;
    }
    
    upstream claude_flow_api {
        server claude-flow-dev:3001;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Main application
        location / {
            proxy_pass http://claude_flow_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
        
        # API endpoints
        location /api/ {
            proxy_pass http://claude_flow_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # API-specific headers
            proxy_set_header Accept application/json;
            proxy_set_header Content-Type application/json;
        }
        
        # Static files
        location /static/ {
            alias /shared/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # Metrics endpoint
        location /metrics {
            alias /shared/metrics.json;
            add_header Content-Type application/json;
        }
    }
}