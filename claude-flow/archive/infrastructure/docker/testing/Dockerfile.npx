# Docker environment for testing npx claude-flow@2.0.0
FROM node:20-alpine

# Install dependencies
RUN apk add --no-cache git bash curl jq

# Create test directory
WORKDIR /test

# Install claude-flow globally via npx (cache it)
RUN npx claude-flow@2.0.0 --version || true

# Create test scripts directory
RUN mkdir -p /test/scripts /test/results /test/logs

# Copy test script
COPY test-mcp-tools.sh /test/scripts/
RUN chmod +x /test/scripts/test-mcp-tools.sh

# Default command
CMD ["/test/scripts/test-mcp-tools.sh", "npx"]