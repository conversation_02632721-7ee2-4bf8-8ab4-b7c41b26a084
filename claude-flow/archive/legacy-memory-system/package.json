{"name": "@claude-flow/memory", "version": "1.0.0", "description": "SPARC Memory Bank - Persistent memory system for Claude-Flow", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "lint": "eslint src --ext .ts", "clean": "rm -rf dist coverage"}, "keywords": ["memory", "storage", "crdt", "sqlite", "markdown", "cache", "replication", "vector-search"], "author": "rUv", "license": "MIT", "dependencies": {"better-sqlite3": "^9.2.2", "uuid": "^9.0.1", "js-yaml": "^4.1.0", "axios": "^1.6.2"}, "devDependencies": {"@types/better-sqlite3": "^7.6.8", "@types/js-yaml": "^4.0.9", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "typescript": "^5.3.3", "vitest": "^1.1.0", "@vitest/coverage-v8": "^1.1.0", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}, "engines": {"node": ">=18.0.0"}}