{"timestamp": "2025-07-01T23:10:42.020Z", "version": "1.0", "entries": [{"id": "entry_mcl52xul_cyffbau3x", "key": "test-key", "value": "test-value", "type": "string", "namespace": "default", "tags": [], "metadata": {}, "owner": "system", "accessLevel": "shared", "createdAt": "2025-07-01T23:08:43.101Z", "updatedAt": "2025-07-01T23:08:43.101Z", "lastAccessedAt": "2025-07-01T23:08:51.106Z", "version": 1, "size": 41, "compressed": false, "checksum": "d1cae01b21c77850707c5b75192b6efdb722169b59dce6bce7fb938b64df9c1a", "references": [], "dependencies": []}, {"id": "entry_mcl54eyc_c4g9ve24a", "key": "perf-test", "value": "Performance test data", "type": "string", "namespace": "default", "tags": [], "metadata": {}, "owner": "system", "accessLevel": "shared", "createdAt": "2025-07-01T23:09:51.924Z", "updatedAt": "2025-07-01T23:09:51.924Z", "lastAccessedAt": "2025-07-01T23:09:51.924Z", "version": 1, "size": 52, "compressed": false, "checksum": "9253531d0f5e676dde2d8d6fe61884c2c8b91a9a72aa826c3b65723eac344869", "references": [], "dependencies": []}, {"id": "entry_mcl55hlu_urt8suv3j", "key": "comprehensive-test-results", "value": "\"{\\n  \\\\\\\"testSession\\\\\\\": \\\\\\\"2025-07-01T23:08:12Z\\\\\\\",\\n  \\\\\\\"testCategories\\\\\\\": {\\n    \\\\\\\"coreCommands\\\\\\\": {\\n      \\\\\\\"status\\\\\\\": \\\\\\\"PASSED\\\\\\\",\\n      \\\\\\\"configInit\\\\\\\": \\\\\\\"PASSED\\\\\\\", \\n      \\\\\\\"agentSpawn\\\\\\\": \\\\\\\"PASSED\\\\\\\",\\n      \\\\\\\"memoryStore\\\\\\\": \\\\\\\"PASSED\\\\\\\",\\n      \\\\\\\"memoryGet\\\\\\\": \\\\\\\"PASSED\\\\\\\"\\n    },\\n    \\\\\\\"ruvSwarmIntegration\\\\\\\": {\\n      \\\\\\\"swarmInit\\\\\\\": \\\\\\\"PASSED_WITH_WARNINGS\\\\\\\",\\n      \\\\\\\"swarmStatus\\\\\\\": \\\\\\\"PASSED_WITH_WARNINGS\\\\\\\",\\n      \\\\\\\"hooks\\\\\\\": \\\\\\\"FAILED\\\\\\\"\\n    },\\n    \\\\\\\"performance\\\\\\\": {\\n      \\\\\\\"statusResponseTime\\\\\\\": \\\\\\\"1.146s_GOOD\\\\\\\",\\n      \\\\\\\"memoryPerformance\\\\\\\": \\\\\\\"1.069s_GOOD\\\\\\\",\\n      \\\\\\\"memoryUsage\\\\\\\": \\\\\\\"768K_EFFICIENT\\\\\\\"\\n    },\\n    \\\\\\\"integration\\\\\\\": {\\n      \\\\\\\"sparcModes\\\\\\\": \\\\\\\"PASSED\\\\\\\",\\n      \\\\\\\"sparcModesList\\\\\\\": \\\\\\\"PASSED\\\\\\\",\\n      \\\\\\\"mcpServer\\\\\\\": \\\\\\\"PASSED\\\\\\\",\\n      \\\\\\\"agentCoordination\\\\\\\": \\\\\\\"PASSED\\\\\\\"\\n    }\\n  },\\n  \\\\\\\"summary\\\\\\\": \\\\\\\"Most core functionality working, minor WASM warnings, hooks need fixing\\\\\\\"\\n}\"", "type": "string", "namespace": "default", "tags": [], "metadata": {}, "owner": "system", "accessLevel": "shared", "createdAt": "2025-07-01T23:10:42.018Z", "updatedAt": "2025-07-01T23:10:42.018Z", "lastAccessedAt": "2025-07-01T23:10:42.018Z", "version": 1, "size": 1424, "compressed": true, "checksum": "3cef23d9b4e24b83cfa80ef622b1c2308f84c4f900925b586dd59590ab193831", "references": [], "dependencies": []}], "statistics": {"overview": {"totalEntries": 3, "totalSize": 1517, "compressedEntries": 1, "compressionRatio": -14.311827956989248, "indexSize": 150, "memoryUsage": 9582120, "diskUsage": 0}, "distribution": {"byNamespace": {"default": {"count": 3, "size": 1517}}, "byType": {"string": {"count": 3, "size": 1517}}, "byOwner": {"system": {"count": 3, "size": 1517}}, "byAccessLevel": {"shared": {"count": 3, "size": 1517}}}, "temporal": {"entriesCreatedLast24h": 3, "entriesUpdatedLast24h": 3, "entriesAccessedLast24h": 3, "oldestEntry": "2025-07-01T23:08:43.101Z", "newestEntry": "2025-07-01T23:10:42.018Z"}, "performance": {"averageQueryTime": 0, "averageWriteTime": 1, "cacheHitRatio": 0, "indexEfficiency": 0.95}, "health": {"expiredEntries": 0, "orphanedReferences": 0, "duplicateKeys": 0, "corruptedEntries": 0, "recommendedCleanup": false}, "optimization": {"suggestions": [], "potentialSavings": {"compression": 0, "cleanup": 0, "deduplication": 0}, "indexOptimization": ["Consider periodic index rebuilding for optimal performance"]}}}