{"mcpServers": {"task-master-ai": {"type": "stdio", "command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "YOUR_ANTHROPIC_API_KEY_HERE", "PERPLEXITY_API_KEY": "YOUR_PERPLEXITY_API_KEY_HERE", "OPENAI_API_KEY": "YOUR_OPENAI_KEY_HERE", "GOOGLE_API_KEY": "YOUR_GOOGLE_KEY_HERE", "XAI_API_KEY": "YOUR_XAI_KEY_HERE", "OPENROUTER_API_KEY": "YOUR_OPENROUTER_KEY_HERE", "MISTRAL_API_KEY": "YOUR_MISTRAL_KEY_HERE", "AZURE_OPENAI_API_KEY": "YOUR_AZURE_KEY_HERE", "OLLAMA_API_KEY": "YOUR_OLLAMA_API_KEY_HERE"}}, "dart-file-system": {"type": "stdio", "command": "/Users/<USER>/claudia/run_dart_mcp.sh", "args": [], "env": {}}, "playwright": {"type": "stdio", "command": "npx", "args": ["-y", "@playwright/mcp@latest"], "env": {"PLAYWRIGHT_HEADLESS": "true"}}, "database-server": {"type": "stdio", "command": "npx", "args": ["-y", "@executeautomation/database-server"], "env": {}}, "sqlite": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"], "env": {}}, "memory": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {}}, "sequential-thinking": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "git": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-git"], "env": {}}, "github": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "YOUR_GITHUB_TOKEN_HERE"}}, "fetch": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"], "env": {}}, "brave-search": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "YOUR_BRAVE_API_KEY_HERE"}}, "puppeteer": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {}}, "filesystem": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>"], "env": {}}, "time": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-time"], "env": {}}, "activity-mcp": {"type": "stdio", "command": "npx", "args": ["-y", "activity-mcp"], "env": {}, "disabled": true, "alwaysAllow": []}, "mobile-mcp": {"type": "stdio", "command": "npx", "args": ["-y", "mobile-mcp"], "env": {}, "disabled": true, "alwaysAllow": []}, "gistpad-mcp": {"type": "stdio", "command": "npx", "args": ["-y", "gistpad-mcp"], "env": {}, "disabled": true, "alwaysAllow": []}, "claude-flow": {"type": "stdio", "command": "npx", "args": ["claude-flow", "mcp-server"], "env": {"CLAUDE_FLOW_PORT": "8765", "CLAUDE_FLOW_HOST": "localhost", "NODE_ENV": "production"}, "disabled": false, "alwaysAllow": []}}}